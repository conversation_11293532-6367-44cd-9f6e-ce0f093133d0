# dcbg-dcf-bcclient-sandbox

## 設計資料

API 概要設計: https://decurret.atlassian.net/wiki/spaces/DIG/pages/1146748941/BC+API

内部処理設計: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2613706768/BC+API

## 環境構築

Confluence ページを参照
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1439400032/BC

## 起動（Docker）

```
./scripts/run-local.sh
```

## ビルド

```
./gradlew build
```

## ユニットテスト

```
./gradlew test
```

## 起動（Docker マルチコンテナ）

複数台で負荷分散した際のスループット計測（Confluence）
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BC+Besu

```
./scripts/run-multi-app-local.sh
```

## テスト

### send API

例

```
curl -X POST http://localhost:8081/send \
-H "Content-Type: application/json" \
-d '{"contractName": "Provider", "method": "addProvider", "args": { "provID" : "101", "deadline": "xxxxxxxx", "signature": "xxxxxxxx" }}'
```

### call API

例

```
curl -X POST http://localhost:8081/call \
-H "Content-Type: application/json" \
-d '{"contractName": "Provider", "method": "hasProvID", "args": { "provID" : "101", "chkEnabled" : true }}'
```

### transfer

例

```
curl -X POST http://localhost:8081/send \
    -H "Content-Type: application/json" \
    -d '{"contractName": "Token", "method": "transferSingle", "args": {"validatorId": 13107, "fromAccountId": 20481, "sendAccountId": 20481, "toAccountId": 20480, "amount": 1, "miscValue1": "0x0000000000000000000000000000000000000000000000000000000000000000", "miscValue2": "0x0000000000000000000000000000000000000000000000000000000000000000", "tokenId": 36864, "deadline": **********, "signature": "0x0cc895a10c0047cc7c61500137b9fe640fea3ca0a0fd5e970c0cbb5357244f99433ba024ec871564aa362cee5a62617c1790545c8b1e77ee86c85feb8a0778c41b"}}'
```
