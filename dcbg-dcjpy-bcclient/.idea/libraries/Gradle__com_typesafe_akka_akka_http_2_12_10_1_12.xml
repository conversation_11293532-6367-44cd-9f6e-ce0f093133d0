<component name="libraryTable">
  <library name="Gradle: com.typesafe.akka:akka-http_2.12:10.1.12" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe.akka" artifactId="akka-http_2.12" version="10.1.12" baseVersion="10.1.12" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-http_2.12/10.1.12/5878ede457fb801e540c2d2231fc1efa8cd9741c/akka-http_2.12-10.1.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-http_2.12/10.1.12/d035a7b2a949ebcc0f4efd74a2d7a3067dd9a6fb/akka-http_2.12-10.1.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-http_2.12/10.1.12/a002f45ebb41277a8899c8a49a22bc4e2b60a037/akka-http_2.12-10.1.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>