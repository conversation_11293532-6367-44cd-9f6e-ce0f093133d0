<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-beans:5.3.24" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-beans" version="5.3.24" baseVersion="5.3.24" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-beans/5.3.24/e487ea6de09b9a7c36548028feeafa511a593532/spring-beans-5.3.24.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-beans/5.3.24/2479d8d231e16b273c923b67f84c93aa459d740b/spring-beans-5.3.24-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-beans/5.3.24/9599830021a16b6d6048b46700c5ed8fd5fd80bb/spring-beans-5.3.24-sources.jar!/" />
    </SOURCES>
  </library>
</component>