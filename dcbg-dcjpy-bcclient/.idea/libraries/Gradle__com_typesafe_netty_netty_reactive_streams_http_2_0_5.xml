<component name="libraryTable">
  <library name="Gradle: com.typesafe.netty:netty-reactive-streams-http:2.0.5" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe.netty" artifactId="netty-reactive-streams-http" version="2.0.5" baseVersion="2.0.5" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.netty/netty-reactive-streams-http/2.0.5/5148eda52181bf504eaf61015f6cf0f39ea22136/netty-reactive-streams-http-2.0.5.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.netty/netty-reactive-streams-http/2.0.5/91d75f0dcec3562e234129b4c4a2c221a64d7da5/netty-reactive-streams-http-2.0.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.netty/netty-reactive-streams-http/2.0.5/ed1ae857a3368758c7d3c75b30d11b83153d7c71/netty-reactive-streams-http-2.0.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>