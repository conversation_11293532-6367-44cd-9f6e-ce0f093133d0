<component name="libraryTable">
  <library name="Gradle: org.junit.jupiter:junit-jupiter:5.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.jupiter" artifactId="junit-jupiter" version="5.8.2" baseVersion="5.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.8.2/5a817b1e63f1217e5c586090c45e681281f097ad/junit-jupiter-5.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.8.2/3c791495768512669e0463f7be775c1ee4ea10c3/junit-jupiter-5.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.8.2/5fd01c33132462aab3905cb74bd1097a713ab91a/junit-jupiter-5.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>