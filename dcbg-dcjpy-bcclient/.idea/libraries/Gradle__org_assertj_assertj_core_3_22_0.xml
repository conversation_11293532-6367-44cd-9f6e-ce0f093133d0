<component name="libraryTable">
  <library name="Gradle: org.assertj:assertj-core:3.22.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.assertj" artifactId="assertj-core" version="3.22.0" baseVersion="3.22.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.assertj/assertj-core/3.22.0/c300c0c6a24559f35fa0bd3a5472dc1edcd0111e/assertj-core-3.22.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.assertj/assertj-core/3.22.0/14e1aeedfc69f7779651aae076cc34a3e91b4ab5/assertj-core-3.22.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.assertj/assertj-core/3.22.0/2b9454bcf528122cfb8aaffa2f02e1c1dd4d3ec5/assertj-core-3.22.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>