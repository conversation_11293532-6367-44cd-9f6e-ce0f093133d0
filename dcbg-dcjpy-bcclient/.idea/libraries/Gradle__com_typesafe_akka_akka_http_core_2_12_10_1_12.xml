<component name="libraryTable">
  <library name="Gradle: com.typesafe.akka:akka-http-core_2.12:10.1.12" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe.akka" artifactId="akka-http-core_2.12" version="10.1.12" baseVersion="10.1.12" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-http-core_2.12/10.1.12/9e5c93b1f656e8163239cfa97fe3d4d510b5543f/akka-http-core_2.12-10.1.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-http-core_2.12/10.1.12/8366f0a455d1dc6e074f987bd4db588eca0e32f2/akka-http-core_2.12-10.1.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-http-core_2.12/10.1.12/e37c29cdace7712824f2167b5dd0171b4303fa6e/akka-http-core_2.12-10.1.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>