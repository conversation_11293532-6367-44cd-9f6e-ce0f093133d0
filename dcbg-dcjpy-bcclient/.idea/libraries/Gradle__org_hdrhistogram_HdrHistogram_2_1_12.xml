<component name="libraryTable">
  <library name="Gradle: org.hdrhistogram:HdrHistogram:2.1.12" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.hdrhistogram" artifactId="HdrHistogram" version="2.1.12" baseVersion="2.1.12" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hdrhistogram/HdrHistogram/2.1.12/6eb7552156e0d517ae80cc2247be1427c8d90452/HdrHistogram-2.1.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hdrhistogram/HdrHistogram/2.1.12/e660fe1e5e6ae539f57fc98196a0a7192fe922da/HdrHistogram-2.1.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hdrhistogram/HdrHistogram/2.1.12/d8cf7bad79ef75fd41395c5f464ea38777463d95/HdrHistogram-2.1.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>