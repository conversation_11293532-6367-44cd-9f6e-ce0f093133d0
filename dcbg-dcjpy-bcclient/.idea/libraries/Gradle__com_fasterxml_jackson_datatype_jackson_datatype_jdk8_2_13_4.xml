<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.datatype" artifactId="jackson-datatype-jdk8" version="2.13.4" baseVersion="2.13.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jdk8/2.13.4/557dbba5d8dfc7b7f944c58fe084109afcb5670b/jackson-datatype-jdk8-2.13.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jdk8/2.13.4/50ae297efd197a02269c41aa3615999462d39a0e/jackson-datatype-jdk8-2.13.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jdk8/2.13.4/4cf0dea5221013dcffa6e4c659488fef60e38141/jackson-datatype-jdk8-2.13.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>