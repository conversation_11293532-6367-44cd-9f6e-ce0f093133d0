<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:utils:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="utils" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/utils/2.16.78/dee99b1f7f2a528a343a11f5913834c66469086f/utils-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/utils/2.16.78/12bc0690d40657f330f0e3e119554dba54a4de49/utils-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/utils/2.16.78/ec19038400354f57a859a9ff8ef05a2698861114/utils-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>