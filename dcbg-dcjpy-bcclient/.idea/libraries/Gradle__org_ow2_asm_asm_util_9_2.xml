<component name="libraryTable">
  <library name="Gradle: org.ow2.asm:asm-util:9.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.ow2.asm" artifactId="asm-util" version="9.2" baseVersion="9.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-util/9.2/fbc178fc5ba3dab50fd7e8a5317b8b647c8e8946/asm-util-9.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-util/9.2/6a25f5d601ccd3137bc3637ac94bb7592c6287fc/asm-util-9.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-util/9.2/f04d12daefcb3cf02ef27e67c1ccbac24d2f5c6f/asm-util-9.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>