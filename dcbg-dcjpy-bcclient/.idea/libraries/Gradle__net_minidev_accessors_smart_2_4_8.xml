<component name="libraryTable">
  <library name="Gradle: net.minidev:accessors-smart:2.4.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="net.minidev" artifactId="accessors-smart" version="2.4.8" baseVersion="2.4.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/accessors-smart/2.4.8/6e1bee5a530caba91893604d6ab41d0edcecca9a/accessors-smart-2.4.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/accessors-smart/2.4.8/996698c7961185135f33c17c4a81f16c238c9b24/accessors-smart-2.4.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/accessors-smart/2.4.8/f8801f70a860e4e3a9485f9923dde4204cb70c96/accessors-smart-2.4.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>