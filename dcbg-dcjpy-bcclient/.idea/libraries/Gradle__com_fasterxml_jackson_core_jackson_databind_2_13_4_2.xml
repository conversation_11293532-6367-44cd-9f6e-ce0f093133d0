<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.core:jackson-databind:2.13.4.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-databind" version="2.13.4.2" baseVersion="2.13.4.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.13.4.2/325c06bdfeb628cfb80ebaaf1a26cc1eb558a585/jackson-databind-2.13.4.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.13.4.2/4d72a69b89d62a017aca18a98896adacf99127a5/jackson-databind-2.13.4.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.13.4.2/99771ed18f5059ca6b4fb51142d7aa4a2eba9002/jackson-databind-2.13.4.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>