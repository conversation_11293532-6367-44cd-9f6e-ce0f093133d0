<component name="libraryTable">
  <library name="Gradle: org.web3j:crypto:4.9.7" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.web3j" artifactId="crypto" version="4.9.7" baseVersion="4.9.7" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/crypto/4.9.7/6e89d2a196844acaa239a1ad872be09d7419c414/crypto-4.9.7.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/crypto/4.9.7/47321a7949ecd10d80eececef48734d5f5d8e966/crypto-4.9.7-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/crypto/4.9.7/c6d6f031c8fd1a8f99956c77f3ccedd815b42f1a/crypto-4.9.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>