<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-actuator:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-actuator" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/2.7.6/b3b0e2877b1da313d662797fc234fc53f5850247/spring-boot-starter-actuator-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/2.7.6/14b4120e87819a58cc3cbaba1856c22de9bb9c6d/spring-boot-starter-actuator-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/2.7.6/4bc177444c4f5058e4733a1c8917dd113d33a5d7/spring-boot-starter-actuator-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>