<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-actuator-autoconfigure:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-actuator-autoconfigure" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator-autoconfigure/2.7.6/2ef8c78bda6268eafc4a3a250557afab8757fa/spring-boot-actuator-autoconfigure-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator-autoconfigure/2.7.6/5f753344957dd9aff10c1af1bb5c7ab433d837d5/spring-boot-actuator-autoconfigure-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator-autoconfigure/2.7.6/e8ba037f6f24204c0d9355ddb902b1445a8604a6/spring-boot-actuator-autoconfigure-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>