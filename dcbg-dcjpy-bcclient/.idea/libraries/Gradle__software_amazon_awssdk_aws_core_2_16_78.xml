<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:aws-core:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="aws-core" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-core/2.16.78/91a2018bfbef73bec74ad2fa6fe01c7d8a5ad61e/aws-core-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-core/2.16.78/a637c7fa6e90f41ff47633a8f8c237d6e9b8148a/aws-core-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-core/2.16.78/44604f52d87764cd87c6e41ba4272766fe98f45b/aws-core-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>