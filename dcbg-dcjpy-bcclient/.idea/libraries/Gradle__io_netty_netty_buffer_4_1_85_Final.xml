<component name="libraryTable">
  <library name="Gradle: io.netty:netty-buffer:4.1.85.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-buffer" version="4.1.85.Final" baseVersion="4.1.85.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.85.Final/ca55790d7c66ac1a4ddd204d98c2a872187a052/netty-buffer-4.1.85.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.85.Final/73780d9828686b1fa01b3756a34cdb972c0715c9/netty-buffer-4.1.85.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.85.Final/d68c16411753a96ca24f1df5732d208dbaa65193/netty-buffer-4.1.85.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>