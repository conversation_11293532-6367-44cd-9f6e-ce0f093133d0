<component name="libraryTable">
  <library name="Gradle: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.69" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.apache.tomcat.embed" artifactId="tomcat-embed-websocket" version="9.0.69" baseVersion="9.0.69" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-websocket/9.0.69/c5da77c17a667b4e7a6bb01fb3b28c46c2717123/tomcat-embed-websocket-9.0.69.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-websocket/9.0.69/f3291c86238c2069b476fff2675cca4509d36157/tomcat-embed-websocket-9.0.69-sources.jar!/" />
    </SOURCES>
  </library>
</component>