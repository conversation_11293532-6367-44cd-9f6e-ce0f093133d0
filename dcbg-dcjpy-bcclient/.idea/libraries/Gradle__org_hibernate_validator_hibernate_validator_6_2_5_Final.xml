<component name="libraryTable">
  <library name="Gradle: org.hibernate.validator:hibernate-validator:6.2.5.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.hibernate.validator" artifactId="hibernate-validator" version="6.2.5.Final" baseVersion="6.2.5.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hibernate.validator/hibernate-validator/6.2.5.Final/a68959c06e5f8ff45faff469aa16f232c04af620/hibernate-validator-6.2.5.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hibernate.validator/hibernate-validator/6.2.5.Final/63cae51be197f942db543d5e2247eeaa79433849/hibernate-validator-6.2.5.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>