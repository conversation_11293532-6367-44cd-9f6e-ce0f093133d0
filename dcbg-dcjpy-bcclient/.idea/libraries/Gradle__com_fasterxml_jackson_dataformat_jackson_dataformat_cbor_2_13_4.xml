<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.13.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.dataformat" artifactId="jackson-dataformat-cbor" version="2.13.4" baseVersion="2.13.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-cbor/2.13.4/ccaf21e6a02a20cae6591a12d20bf310544cf3ee/jackson-dataformat-cbor-2.13.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-cbor/2.13.4/4ca352538ca2134195fc3b04c87ce230327e15e5/jackson-dataformat-cbor-2.13.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-cbor/2.13.4/c1c044fa0fde0d5a22f098185c13ca745f9a1bfe/jackson-dataformat-cbor-2.13.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>