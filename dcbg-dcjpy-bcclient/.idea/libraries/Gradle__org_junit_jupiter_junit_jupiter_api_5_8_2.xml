<component name="libraryTable">
  <library name="Gradle: org.junit.jupiter:junit-jupiter-api:5.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.jupiter" artifactId="junit-jupiter-api" version="5.8.2" baseVersion="5.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-api/5.8.2/4c21029217adf07e4c0d0c5e192b6bf610c94bdc/junit-jupiter-api-5.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-api/5.8.2/a75d6242107e80c00a86574b2b5de89c904724e1/junit-jupiter-api-5.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-api/5.8.2/429d353563c5fb35a3d47076bedbf75161e976c2/junit-jupiter-api-5.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>