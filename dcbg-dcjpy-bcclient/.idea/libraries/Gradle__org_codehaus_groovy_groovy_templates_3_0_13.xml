<component name="libraryTable">
  <library name="Gradle: org.codehaus.groovy:groovy-templates:3.0.13" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.codehaus.groovy" artifactId="groovy-templates" version="3.0.13" baseVersion="3.0.13" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-templates/3.0.13/fa252df34a67831060c47fd5e00099b6cbef2e31/groovy-templates-3.0.13.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-templates/3.0.13/2ec04519e460960cc62b897785b0e0f13ca06936/groovy-templates-3.0.13-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-templates/3.0.13/91245469afa41a35d363d7f552e6ecb65fe04a0/groovy-templates-3.0.13-sources.jar!/" />
    </SOURCES>
  </library>
</component>