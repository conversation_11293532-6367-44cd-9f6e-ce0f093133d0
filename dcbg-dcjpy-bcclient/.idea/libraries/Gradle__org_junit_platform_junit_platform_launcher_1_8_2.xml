<component name="libraryTable">
  <library name="Gradle: org.junit.platform:junit-platform-launcher:1.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.platform" artifactId="junit-platform-launcher" version="1.8.2" baseVersion="1.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-launcher/1.8.2/c334fcee82b81311ab5c426ec2d52d467c8d0b28/junit-platform-launcher-1.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-launcher/1.8.2/1cabe92733ffa7b26080f3bde55306e3bc09ad04/junit-platform-launcher-1.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-launcher/1.8.2/6cb79f38fd8b2b753d274d80edf00a7462d75461/junit-platform-launcher-1.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>