<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:aws-query-protocol:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="aws-query-protocol" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-query-protocol/2.16.78/1e37bbca45f1f21bd2f2e836253ac940cd5f6692/aws-query-protocol-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-query-protocol/2.16.78/c405bbb2e0ee7007c1ef2cda3def49b1f035fea2/aws-query-protocol-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-query-protocol/2.16.78/c8d4e9654c8d7ccd0f425dadfcaf4ffc3dbed75e/aws-query-protocol-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>