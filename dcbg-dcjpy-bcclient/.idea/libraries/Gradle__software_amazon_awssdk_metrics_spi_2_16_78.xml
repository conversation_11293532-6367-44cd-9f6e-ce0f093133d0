<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:metrics-spi:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="metrics-spi" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/metrics-spi/2.16.78/7d8852a1f61291df49b5a4c161ead101435592b6/metrics-spi-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/metrics-spi/2.16.78/3f6db74f4423d97ba6ab37956d0615e0588d618b/metrics-spi-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/metrics-spi/2.16.78/b858c47dbac3591dfe0812ba207a0de57132da63/metrics-spi-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>