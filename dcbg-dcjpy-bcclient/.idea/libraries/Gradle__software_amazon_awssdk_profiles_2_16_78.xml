<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:profiles:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="profiles" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/profiles/2.16.78/9346bd0b955b592951321bdee6e340b5211258ab/profiles-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/profiles/2.16.78/f1bafc2c8155e1ededf2b4a0d6fea6a05bbd8e2c/profiles-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/profiles/2.16.78/4d292444b7d02196aa18d09fbe95dc54a2e07e88/profiles-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>