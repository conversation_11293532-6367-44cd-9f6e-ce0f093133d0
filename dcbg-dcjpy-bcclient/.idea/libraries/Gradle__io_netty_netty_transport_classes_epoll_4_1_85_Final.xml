<component name="libraryTable">
  <library name="Gradle: io.netty:netty-transport-classes-epoll:4.1.85.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-transport-classes-epoll" version="4.1.85.Final" baseVersion="4.1.85.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.85.Final/8fa9329620accf9ebef76a805cdd1e35cfd8b859/netty-transport-classes-epoll-4.1.85.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.85.Final/8d501978bdc0ad5144eec0bb5bf18e04fa6ca6a8/netty-transport-classes-epoll-4.1.85.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.85.Final/39e9fe421c85f439b663e88e9ba4d795f349c3b2/netty-transport-classes-epoll-4.1.85.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>