<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-validation:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-validation" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-validation/2.7.6/cc43bd3745e36bc741e2cf5f43db09fcac874839/spring-boot-starter-validation-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-validation/2.7.6/8628e000b6f74be572cfee344895b4582a8d125/spring-boot-starter-validation-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-validation/2.7.6/7699b61d545e3ccb4d22b4294ee9b0c13035525e/spring-boot-starter-validation-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>