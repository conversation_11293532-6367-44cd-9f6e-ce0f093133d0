<component name="libraryTable">
  <library name="Gradle: org.iq80.leveldb:leveldb-api:0.12" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.iq80.leveldb" artifactId="leveldb-api" version="0.12" baseVersion="0.12" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.iq80.leveldb/leveldb-api/0.12/c97c934e9de3be7b48f6677385e1294c9ec25cc6/leveldb-api-0.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.iq80.leveldb/leveldb-api/0.12/7fdd762649e924bf51a08fd7efbe0d7f6243e951/leveldb-api-0.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.iq80.leveldb/leveldb-api/0.12/f9fa0513aa62895253d96a8d716ea02773a88293/leveldb-api-0.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>