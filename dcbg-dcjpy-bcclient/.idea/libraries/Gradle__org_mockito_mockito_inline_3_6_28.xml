<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-inline:3.6.28" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.mockito" artifactId="mockito-inline" version="3.6.28" baseVersion="3.6.28" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-inline/3.6.28/3d1dffee9a8a1998ec782383ca2f818848f2d5f1/mockito-inline-3.6.28.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-inline/3.6.28/d7ff0fc68027ec731fcb0b8f5284bf7eb4ca4972/mockito-inline-3.6.28-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-inline/3.6.28/3d1dffee9a8a1998ec782383ca2f818848f2d5f1/mockito-inline-3.6.28-sources.jar!/" />
    </SOURCES>
  </library>
</component>