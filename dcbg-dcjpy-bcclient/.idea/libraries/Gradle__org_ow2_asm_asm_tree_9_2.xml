<component name="libraryTable">
  <library name="Gradle: org.ow2.asm:asm-tree:9.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.ow2.asm" artifactId="asm-tree" version="9.2" baseVersion="9.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-tree/9.2/d96c99a30f5e1a19b0e609dbb19a44d8518ac01e/asm-tree-9.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-tree/9.2/ede38058176b5199dc49b17cdbce82c1972533dd/asm-tree-9.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-tree/9.2/d7c4f2660898c227b2fb209f94ca15ae45722535/asm-tree-9.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>