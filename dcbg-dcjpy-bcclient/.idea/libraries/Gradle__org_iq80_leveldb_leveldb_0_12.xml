<component name="libraryTable">
  <library name="Gradle: org.iq80.leveldb:leveldb:0.12" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.iq80.leveldb" artifactId="leveldb" version="0.12" baseVersion="0.12" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.iq80.leveldb/leveldb/0.12/12ea5d0e5640d91695210bfb065562ee969a25ff/leveldb-0.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.iq80.leveldb/leveldb/0.12/f5b94520822553010a11fd7fb18d01bbad90f59e/leveldb-0.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.iq80.leveldb/leveldb/0.12/8513d835ed5d9cfb3be4dae58128bc55f6aa1531/leveldb-0.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>