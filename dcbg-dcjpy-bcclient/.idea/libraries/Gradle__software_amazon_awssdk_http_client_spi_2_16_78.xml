<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:http-client-spi:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="http-client-spi" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/http-client-spi/2.16.78/264fc0a8ee496a4c633ebf3528d53c95107e54f1/http-client-spi-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/http-client-spi/2.16.78/6838990805f78da86075e919e085a7a620775e4e/http-client-spi-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/http-client-spi/2.16.78/45d97d78ad0e0cb41621966a3ece9f5d0ae7e7d2/http-client-spi-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>