<component name="libraryTable">
  <library name="Gradle: com.github.jnr:jnr-posix:3.1.15" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.github.jnr" artifactId="jnr-posix" version="3.1.15" baseVersion="3.1.15" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-posix/3.1.15/f7d6737adcbd5925d625b8f99166de2cbf13caac/jnr-posix-3.1.15.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-posix/3.1.15/ae34ce0089776e508b6947935deef19a6f028984/jnr-posix-3.1.15-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-posix/3.1.15/6ac479d5526270598c4817e0a1c31bf641bbf68e/jnr-posix-3.1.15-sources.jar!/" />
    </SOURCES>
  </library>
</component>