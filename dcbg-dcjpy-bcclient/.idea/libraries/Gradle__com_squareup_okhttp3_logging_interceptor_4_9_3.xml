<component name="libraryTable">
  <library name="Gradle: com.squareup.okhttp3:logging-interceptor:4.9.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.squareup.okhttp3" artifactId="logging-interceptor" version="4.9.3" baseVersion="4.9.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/logging-interceptor/4.9.3/d7786efea637a62603352985d9a229c206003268/logging-interceptor-4.9.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/logging-interceptor/4.9.3/d5e9086da6f45de1ddbbd89c62288d921d5e7d87/logging-interceptor-4.9.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/logging-interceptor/4.9.3/30288bd77d2f72d76d30688be15ecb7909272dcf/logging-interceptor-4.9.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>