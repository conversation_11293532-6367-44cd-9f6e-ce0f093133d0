<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.module:jackson-module-parameter-names:2.13.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.module" artifactId="jackson-module-parameter-names" version="2.13.4" baseVersion="2.13.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.module/jackson-module-parameter-names/2.13.4/858ccf6624b5fac6044813e845063edb6a62cf37/jackson-module-parameter-names-2.13.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.module/jackson-module-parameter-names/2.13.4/40f8854d40bfff5b3679b2f6dee1cb2492c7253e/jackson-module-parameter-names-2.13.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.module/jackson-module-parameter-names/2.13.4/436f808013c552acb57385765d6dc4cb40b1f178/jackson-module-parameter-names-2.13.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>