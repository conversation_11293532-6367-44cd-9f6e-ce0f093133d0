<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.datatype" artifactId="jackson-datatype-jsr310" version="2.13.4" baseVersion="2.13.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jsr310/2.13.4/e6d820112871f33cd94a1dcc54eef58874753b5/jackson-datatype-jsr310-2.13.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jsr310/2.13.4/f575533ca1e2191837efede8c48cc0d6e1673b9e/jackson-datatype-jsr310-2.13.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jsr310/2.13.4/5624a1fd372ce2b69b9ce25fd4aab4c7aa041811/jackson-datatype-jsr310-2.13.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>