<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:dynamodb:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="dynamodb" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/dynamodb/2.16.78/58ad1a9d6a4e83d146b6af1af50c8983d0569662/dynamodb-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/dynamodb/2.16.78/5bfdd6d9ef9c0c220a9bcc6ad599e4592c343fe0/dynamodb-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/dynamodb/2.16.78/3e1b70a9bcbc3421a3a61a295dfc1033aa57d9be/dynamodb-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>