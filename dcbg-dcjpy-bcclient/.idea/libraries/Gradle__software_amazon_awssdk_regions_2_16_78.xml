<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:regions:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="regions" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/regions/2.16.78/e0883d0a56c38222058d5ba50d34e3ffad988046/regions-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/regions/2.16.78/6fcd90f3b47f30fc1d905ddabb1efe9f85c8abe5/regions-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/regions/2.16.78/9e9a1122a239d11d9093192acaf4bb2604bb2a31/regions-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>