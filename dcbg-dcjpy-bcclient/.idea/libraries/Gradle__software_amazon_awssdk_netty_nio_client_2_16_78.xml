<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:netty-nio-client:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="netty-nio-client" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/netty-nio-client/2.16.78/1514d02fa90122e4fa6d860942f8016bc62ae849/netty-nio-client-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/netty-nio-client/2.16.78/87e9d1ecd1b68fb1c3ebb55af844e76237e479d/netty-nio-client-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/netty-nio-client/2.16.78/e7251961e81eecdbe38ba1c21c9784f86181a1b4/netty-nio-client-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>