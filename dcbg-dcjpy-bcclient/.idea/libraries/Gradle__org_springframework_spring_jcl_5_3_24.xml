<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-jcl:5.3.24" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-jcl" version="5.3.24" baseVersion="5.3.24" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jcl/5.3.24/2b30878663ceed2af07238dc54e92e5bf001438d/spring-jcl-5.3.24.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jcl/5.3.24/daf3b3b20b1d255226ecab48bf49f7d4b5f6847/spring-jcl-5.3.24-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jcl/5.3.24/6248d80458bb41e4bc4c7def09305fb231671401/spring-jcl-5.3.24-sources.jar!/" />
    </SOURCES>
  </library>
</component>