<component name="libraryTable">
  <library name="Gradle: org.junit.jupiter:junit-jupiter-engine:5.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.jupiter" artifactId="junit-jupiter-engine" version="5.8.2" baseVersion="5.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-engine/5.8.2/c598b4328d2f397194d11df3b1648d68d7d990e3/junit-jupiter-engine-5.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-engine/5.8.2/c9ed5fc1cadef3121a3e9bcd4673d0188fd467da/junit-jupiter-engine-5.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-engine/5.8.2/ea87fe3d752e24397f71f347872e79a89c61a704/junit-jupiter-engine-5.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>