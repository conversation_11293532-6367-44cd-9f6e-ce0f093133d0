<component name="libraryTable">
  <library name="Gradle: com.typesafe.akka:akka-actor_2.12:2.5.31" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe.akka" artifactId="akka-actor_2.12" version="2.5.31" baseVersion="2.5.31" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-actor_2.12/2.5.31/a547e19cb9049f8b121a03fc9db0c795bd6acc2a/akka-actor_2.12-2.5.31.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-actor_2.12/2.5.31/e90e40da435eafac87711934e777001ca1b55349/akka-actor_2.12-2.5.31-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-actor_2.12/2.5.31/3ceab60695d6c36d60b44d95fbdb41ff69d0db07/akka-actor_2.12-2.5.31-sources.jar!/" />
    </SOURCES>
  </library>
</component>