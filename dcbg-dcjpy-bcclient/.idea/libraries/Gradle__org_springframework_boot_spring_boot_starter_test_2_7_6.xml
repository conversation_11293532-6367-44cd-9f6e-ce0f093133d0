<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-test:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-test" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/2.7.6/38f4300b939115eae92c1a40ce6e75fe3bbeac00/spring-boot-starter-test-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/2.7.6/3d8ecb3069837e82b01db355ca2a7039c3fe6ce9/spring-boot-starter-test-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/2.7.6/9d7f7030605f36fdbd909f41432a136e8ab71474/spring-boot-starter-test-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>