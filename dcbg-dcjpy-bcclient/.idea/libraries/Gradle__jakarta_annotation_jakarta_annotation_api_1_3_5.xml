<component name="libraryTable">
  <library name="Gradle: jakarta.annotation:jakarta.annotation-api:1.3.5" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.annotation" artifactId="jakarta.annotation-api" version="1.3.5" baseVersion="1.3.5" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/1.3.5/59eb84ee0d616332ff44aba065f3888cf002cd2d/jakarta.annotation-api-1.3.5.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/1.3.5/dce659c5763ffa1e86520cb4d29ca72b89b6605e/jakarta.annotation-api-1.3.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/1.3.5/1ad35f11d17abb52426bfe15ea7b4c583795012/jakarta.annotation-api-1.3.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>