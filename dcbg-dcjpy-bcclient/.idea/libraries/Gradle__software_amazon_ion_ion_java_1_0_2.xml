<component name="libraryTable">
  <library name="Gradle: software.amazon.ion:ion-java:1.0.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.ion" artifactId="ion-java" version="1.0.2" baseVersion="1.0.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.ion/ion-java/1.0.2/ee9dacea7726e495f8352b81c12c23834ffbc564/ion-java-1.0.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.ion/ion-java/1.0.2/6d8ae18a7b9e9f567213ddb41622122623f98cec/ion-java-1.0.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.ion/ion-java/1.0.2/3077203a7e79dd126a480da0491ec60238bbb872/ion-java-1.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>