<component name="libraryTable">
  <library name="Gradle: com.github.jnr:jnr-ffi:2.2.11" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.github.jnr" artifactId="jnr-ffi" version="2.2.11" baseVersion="2.2.11" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-ffi/2.2.11/bcf004ce358c87fc4cd2853b658d336348d0370f/jnr-ffi-2.2.11.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-ffi/2.2.11/142bcdfc25e3b6e2d900ed2d92eebff11862907b/jnr-ffi-2.2.11-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-ffi/2.2.11/10783bbd7f5799dd49659c4be8b60116e9b52b3a/jnr-ffi-2.2.11-sources.jar!/" />
    </SOURCES>
  </library>
</component>