<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-logging:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-logging" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/2.7.6/33a2d9e08d33a6a6f8051ed38daac14046e2f97c/spring-boot-starter-logging-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/2.7.6/a148d5f16c2735c64c3bc8e05d2e7f7d3c9b14ec/spring-boot-starter-logging-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/2.7.6/babaf2faae343c258c64bdb2c31b7e4640a3f5d8/spring-boot-starter-logging-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>