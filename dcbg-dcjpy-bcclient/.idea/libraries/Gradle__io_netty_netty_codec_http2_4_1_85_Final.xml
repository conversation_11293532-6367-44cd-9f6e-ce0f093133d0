<component name="libraryTable">
  <library name="Gradle: io.netty:netty-codec-http2:4.1.85.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-codec-http2" version="4.1.85.Final" baseVersion="4.1.85.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-http2/4.1.85.Final/25203ce1ab68f165b4dce4533102418b49644d91/netty-codec-http2-4.1.85.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-http2/4.1.85.Final/977d1da5be136f99bf9f6c02f0d7f395e2ca981e/netty-codec-http2-4.1.85.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec-http2/4.1.85.Final/b2245f8599ceba25343248ba32eec3dcbb082544/netty-codec-http2-4.1.85.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>