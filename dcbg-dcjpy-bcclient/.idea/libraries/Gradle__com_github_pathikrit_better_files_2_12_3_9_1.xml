<component name="libraryTable">
  <library name="Gradle: com.github.pathikrit:better-files_2.12:3.9.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.github.pathikrit" artifactId="better-files_2.12" version="3.9.1" baseVersion="3.9.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.pathikrit/better-files_2.12/3.9.1/d27d1d4e43577604055d25ebe58be9e935c91035/better-files_2.12-3.9.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.pathikrit/better-files_2.12/3.9.1/b4c6b11677ef47a4968f0fe7399e496d6372326e/better-files_2.12-3.9.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.pathikrit/better-files_2.12/3.9.1/a3bd41e34166a761615d26e039ce3e14400f022e/better-files_2.12-3.9.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>