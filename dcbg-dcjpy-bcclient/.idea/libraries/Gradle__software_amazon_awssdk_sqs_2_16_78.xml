<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:sqs:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="sqs" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sqs/2.16.78/66210adafe7ecae45b9ad5c65870c7fc3b4fa177/sqs-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sqs/2.16.78/54477d9731a9aaa49d997e39e1526dc3806ce41e/sqs-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sqs/2.16.78/3d120a4d6cabc4f4cd3d272abe28cab6b4f87d7f/sqs-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>