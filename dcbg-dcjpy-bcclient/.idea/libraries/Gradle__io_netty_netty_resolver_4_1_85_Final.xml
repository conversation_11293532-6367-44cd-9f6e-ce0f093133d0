<component name="libraryTable">
  <library name="Gradle: io.netty:netty-resolver:4.1.85.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-resolver" version="4.1.85.Final" baseVersion="4.1.85.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.85.Final/aeb5a1de291ac21b9f8d03fa5aa201d109c16d02/netty-resolver-4.1.85.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.85.Final/7786cd37830e95c85dbba204643272d0517abf45/netty-resolver-4.1.85.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.85.Final/a8bdc63c4a7d1d0440bd0c36b90fecc99a19481f/netty-resolver-4.1.85.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>