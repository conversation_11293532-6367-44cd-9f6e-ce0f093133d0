<component name="libraryTable">
  <library name="Gradle: com.amazonaws:aws-java-sdk-s3:1.11.294" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.amazonaws" artifactId="aws-java-sdk-s3" version="1.11.294" baseVersion="1.11.294" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.amazonaws/aws-java-sdk-s3/1.11.294/73978b39ee80d36c16f7f3407d6681c2366830f6/aws-java-sdk-s3-1.11.294.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.amazonaws/aws-java-sdk-s3/1.11.294/b4996743645ecc13fcf3ad6fd52ebea2cf9160aa/aws-java-sdk-s3-1.11.294-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.amazonaws/aws-java-sdk-s3/1.11.294/815c539391a8afbb41cef2052056292a34a99408/aws-java-sdk-s3-1.11.294-sources.jar!/" />
    </SOURCES>
  </library>
</component>