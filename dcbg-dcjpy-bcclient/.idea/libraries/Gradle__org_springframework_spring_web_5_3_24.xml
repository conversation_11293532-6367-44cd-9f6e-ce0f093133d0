<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-web:5.3.24" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-web" version="5.3.24" baseVersion="5.3.24" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-web/5.3.24/d89bbcaabb1ff247a089875cbc4211bfe96c9a59/spring-web-5.3.24.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-web/5.3.24/62c3e867b81b87765942c37a489d74606b37c990/spring-web-5.3.24-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-web/5.3.24/6cd791c2d4822655a504537d15919be1a4a3e5b9/spring-web-5.3.24-sources.jar!/" />
    </SOURCES>
  </library>
</component>