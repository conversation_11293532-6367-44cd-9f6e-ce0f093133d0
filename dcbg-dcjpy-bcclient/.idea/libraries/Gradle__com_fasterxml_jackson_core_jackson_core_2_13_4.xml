<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.core:jackson-core:2.13.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-core" version="2.13.4" baseVersion="2.13.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.13.4/cf934c681294b97ef6d80082faeefbe1edadf56/jackson-core-2.13.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.13.4/fa52b297a184a0aff95e323e0097d2241b9df591/jackson-core-2.13.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.13.4/dd8da2766363ea64383d1344349acce91d5a5469/jackson-core-2.13.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>