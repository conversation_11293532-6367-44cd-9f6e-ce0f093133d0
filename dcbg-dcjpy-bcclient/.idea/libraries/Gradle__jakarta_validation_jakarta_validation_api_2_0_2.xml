<component name="libraryTable">
  <library name="Gradle: jakarta.validation:jakarta.validation-api:2.0.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.validation" artifactId="jakarta.validation-api" version="2.0.2" baseVersion="2.0.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.validation/jakarta.validation-api/2.0.2/5eacc6522521f7eacb081f95cee1e231648461e7/jakarta.validation-api-2.0.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.validation/jakarta.validation-api/2.0.2/797faa9f460e924c345d17727b1f753a1d5f0b6b/jakarta.validation-api-2.0.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.validation/jakarta.validation-api/2.0.2/ef429a7f3bc2d2d1784a1135668a857a6aaedf31/jakarta.validation-api-2.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>