<component name="libraryTable">
  <library name="Gradle: org.ow2.asm:asm-analysis:9.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.ow2.asm" artifactId="asm-analysis" version="9.2" baseVersion="9.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-analysis/9.2/7487dd756daf96cab9986e44b9d7bcb796a61c10/asm-analysis-9.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-analysis/9.2/ffcdac47db150e450c57970dac760206004ad82b/asm-analysis-9.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-analysis/9.2/296a49fa21d815fc92eec1007ea2708983e8df2c/asm-analysis-9.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>