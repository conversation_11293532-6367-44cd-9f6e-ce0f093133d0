<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-web:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-web" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web/2.7.6/9d0166a0390df593243bdc798ca7a1db20ddea9c/spring-boot-starter-web-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web/2.7.6/983bf88453c0b402eed95a41a17d2477a7476e41/spring-boot-starter-web-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web/2.7.6/305981f47d4519de78561f6818a34261aefecc04/spring-boot-starter-web-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>