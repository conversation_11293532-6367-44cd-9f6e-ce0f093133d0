<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-autoconfigure:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-autoconfigure" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/2.7.6/4ad7e89accd25c69bfe84f52202deaf09430be2/spring-boot-autoconfigure-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/2.7.6/88b13b7b693e50079ae4f02939d26869c47d9ff2/spring-boot-autoconfigure-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/2.7.6/27599686ff381c1c501cf3fbd1a377e2bcc50056/spring-boot-autoconfigure-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>