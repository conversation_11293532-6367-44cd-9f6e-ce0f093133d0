<component name="libraryTable">
  <library name="Gradle: org.apache.logging.log4j:log4j-api:2.17.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.apache.logging.log4j" artifactId="log4j-api" version="2.17.2" baseVersion="2.17.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-api/2.17.2/f42d6afa111b4dec5d2aea0fe2197240749a4ea6/log4j-api-2.17.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-api/2.17.2/c3790eb392bad23fcdea595e23c4b3a26bc451c4/log4j-api-2.17.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-api/2.17.2/91a89f75de4f567c38dc7ec9f83f2a1afd2c2915/log4j-api-2.17.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>