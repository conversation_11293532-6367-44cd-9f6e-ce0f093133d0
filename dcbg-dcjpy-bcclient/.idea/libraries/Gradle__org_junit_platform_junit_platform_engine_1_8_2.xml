<component name="libraryTable">
  <library name="Gradle: org.junit.platform:junit-platform-engine:1.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.platform" artifactId="junit-platform-engine" version="1.8.2" baseVersion="1.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-engine/1.8.2/b737de09f19864bd136805c84df7999a142fec29/junit-platform-engine-1.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-engine/1.8.2/8ffc510b691119e4e25c2f14902a837298d55c5d/junit-platform-engine-1.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-engine/1.8.2/ef25ff956479198a71a7d1b23e11f27fb09393b6/junit-platform-engine-1.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>