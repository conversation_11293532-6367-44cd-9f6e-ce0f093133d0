<component name="libraryTable">
  <library name="Gradle: org.junit.jupiter:junit-jupiter-params:5.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.jupiter" artifactId="junit-jupiter-params" version="5.8.2" baseVersion="5.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-params/5.8.2/ddeafe92fc263f895bfb73ffeca7fd56e23c2cce/junit-jupiter-params-5.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-params/5.8.2/ae9167fe224725dc9f0f1f75d5977ad6df299a00/junit-jupiter-params-5.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-params/5.8.2/26d07692b618b8da29479febec7d55e18107ee48/junit-jupiter-params-5.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>