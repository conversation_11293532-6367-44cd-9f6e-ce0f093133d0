<component name="libraryTable">
  <library name="Gradle: org.codehaus.groovy:groovy-json:3.0.13" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.codehaus.groovy" artifactId="groovy-json" version="3.0.13" baseVersion="3.0.13" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-json/3.0.13/21a3d9aeaf975d40d82c4d497e29d9feff066292/groovy-json-3.0.13.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-json/3.0.13/913f4da33e298e9f1fdbf9a8228f8d61defa73fd/groovy-json-3.0.13-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-json/3.0.13/1a87500c1500990559a403e0f1e8536fb177d678/groovy-json-3.0.13-sources.jar!/" />
    </SOURCES>
  </library>
</component>