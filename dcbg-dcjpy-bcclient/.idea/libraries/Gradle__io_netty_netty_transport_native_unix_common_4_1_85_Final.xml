<component name="libraryTable">
  <library name="Gradle: io.netty:netty-transport-native-unix-common:4.1.85.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-transport-native-unix-common" version="4.1.85.Final" baseVersion="4.1.85.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.85.Final/a9c22785e2a4301f523cc9711b387f38a19d8bb2/netty-transport-native-unix-common-4.1.85.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.85.Final/69a1c861d79b4b1f5b8b7f72066335b08bee316/netty-transport-native-unix-common-4.1.85.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.85.Final/4199a89a27c6e461d6ff2b29a08087a529844925/netty-transport-native-unix-common-4.1.85.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>