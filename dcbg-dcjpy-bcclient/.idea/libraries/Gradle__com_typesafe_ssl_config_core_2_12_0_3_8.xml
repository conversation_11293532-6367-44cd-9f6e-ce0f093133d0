<component name="libraryTable">
  <library name="Gradle: com.typesafe:ssl-config-core_2.12:0.3.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe" artifactId="ssl-config-core_2.12" version="0.3.8" baseVersion="0.3.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe/ssl-config-core_2.12/0.3.8/69ee3e1e3a6057372bbaeb9d107d4f6cf9311800/ssl-config-core_2.12-0.3.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe/ssl-config-core_2.12/0.3.8/f1faaf6490d5abd6effa972ffe6323f668b21f99/ssl-config-core_2.12-0.3.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe/ssl-config-core_2.12/0.3.8/e5a918d06c546f2da1173041bc521a40e132a2f8/ssl-config-core_2.12-0.3.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>