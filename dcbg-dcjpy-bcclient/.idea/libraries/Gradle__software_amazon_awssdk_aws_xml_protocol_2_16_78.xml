<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:aws-xml-protocol:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="aws-xml-protocol" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-xml-protocol/2.16.78/313e178a739eca3a73724bc0a6e3b0b0320fea2e/aws-xml-protocol-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-xml-protocol/2.16.78/1c219f5532072feaf0567e10c2e564a3d29a30d8/aws-xml-protocol-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-xml-protocol/2.16.78/a42a1b64be4c861234879a100d5992ff10a03f90/aws-xml-protocol-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>