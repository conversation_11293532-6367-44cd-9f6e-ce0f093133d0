<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:sts:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="sts" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sts/2.16.78/146f4efcd5691ee2194b6266faf23367477d3ae3/sts-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sts/2.16.78/3d237ca481cc6a9cf00e9e4740190a83b788e300/sts-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sts/2.16.78/39ab574b23a0257816fdd3f5deca088df72f1623/sts-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>