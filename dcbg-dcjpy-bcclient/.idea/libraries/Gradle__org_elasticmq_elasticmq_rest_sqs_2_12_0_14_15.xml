<component name="libraryTable">
  <library name="Gradle: org.elasticmq:elasticmq-rest-sqs_2.12:0.14.15" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.elasticmq" artifactId="elasticmq-rest-sqs_2.12" version="0.14.15" baseVersion="0.14.15" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-rest-sqs_2.12/0.14.15/307aeda47df1bd2cd18fc5e83522fee0bc69a42e/elasticmq-rest-sqs_2.12-0.14.15.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-rest-sqs_2.12/0.14.15/5b2c833677ec0d1c11079ea5a5345f87923d2b30/elasticmq-rest-sqs_2.12-0.14.15-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-rest-sqs_2.12/0.14.15/ef9edce7e27b0d849377d889e6bbb915630551a7/elasticmq-rest-sqs_2.12-0.14.15-sources.jar!/" />
    </SOURCES>
  </library>
</component>