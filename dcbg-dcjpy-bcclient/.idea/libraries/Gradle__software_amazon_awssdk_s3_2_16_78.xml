<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:s3:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="s3" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/s3/2.16.78/7cadd81727f9f88c4b346c1c20ec8435cfed42df/s3-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/s3/2.16.78/14a8c463e0197d6325c239b652a417aa1b2168a1/s3-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/s3/2.16.78/fcda68a53465ca630f2d3137b2cd31b32f78640d/s3-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>