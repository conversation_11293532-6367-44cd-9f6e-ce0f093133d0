<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-test:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-test" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/2.7.6/d4f2f397cc9351b6fc37278f482d950a3dc30157/spring-boot-test-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/2.7.6/ad23f056548cdf0309f61fddb0e2ab9a609dae2a/spring-boot-test-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/2.7.6/97c74cdacf534fd51e463f6dfbf4be6abbdeca13/spring-boot-test-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>