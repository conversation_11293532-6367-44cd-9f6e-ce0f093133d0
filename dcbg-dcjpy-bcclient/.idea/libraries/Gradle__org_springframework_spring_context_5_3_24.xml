<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-context:5.3.24" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-context" version="5.3.24" baseVersion="5.3.24" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-context/5.3.24/e48634d7b8f40d4d0fe978830be0247bfc2ff2cd/spring-context-5.3.24.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-context/5.3.24/ab42fdf5e9338396d2eff8eaaf7e38c4b2e4f02b/spring-context-5.3.24-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-context/5.3.24/cfd8e25900c05273cb3141d403b58276fc4d32b2/spring-context-5.3.24-sources.jar!/" />
    </SOURCES>
  </library>
</component>