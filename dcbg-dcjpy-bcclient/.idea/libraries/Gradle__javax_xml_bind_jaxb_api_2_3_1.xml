<component name="libraryTable">
  <library name="Gradle: javax.xml.bind:jaxb-api:2.3.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="javax.xml.bind" artifactId="jaxb-api" version="2.3.1" baseVersion="2.3.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/javax.xml.bind/jaxb-api/2.3.1/8531ad5ac454cc2deb9d4d32c40c4d7451939b5d/jaxb-api-2.3.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/javax.xml.bind/jaxb-api/2.3.1/22a0f33c1fd75548cb4338161a73434d34d84124/jaxb-api-2.3.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/javax.xml.bind/jaxb-api/2.3.1/e47264d065cecbc572ba99856fcc7da19a470ae3/jaxb-api-2.3.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>