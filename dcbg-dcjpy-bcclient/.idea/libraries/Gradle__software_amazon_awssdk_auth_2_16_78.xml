<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:auth:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="auth" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/auth/2.16.78/ba8603929541eab31623705d190a33a1059fc125/auth-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/auth/2.16.78/a7225645c570d747ed18dafc295cef97ffe877c3/auth-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/auth/2.16.78/48d0aa5176f8f9de68a928817bce666517951235/auth-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>