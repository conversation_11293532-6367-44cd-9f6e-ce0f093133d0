<component name="libraryTable">
  <library name="Gradle: io.micrometer:micrometer-core:1.9.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.micrometer" artifactId="micrometer-core" version="1.9.6" baseVersion="1.9.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-core/1.9.6/65ca036d01b29954554b92fce3166929c045012e/micrometer-core-1.9.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-core/1.9.6/c4e3b2bbc8ea58382d6be350d4450e5295eff682/micrometer-core-1.9.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-core/1.9.6/9c4977704eaa95dab196fa268f29a661f0f2233d/micrometer-core-1.9.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>