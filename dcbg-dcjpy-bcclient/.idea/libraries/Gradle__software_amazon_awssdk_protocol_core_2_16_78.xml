<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:protocol-core:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="protocol-core" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/protocol-core/2.16.78/9ff55e5fad866ccd265c4f732f1b74512485c233/protocol-core-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/protocol-core/2.16.78/e258672d4509440e7642361677bd05fde0cee3a8/protocol-core-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/protocol-core/2.16.78/8919cd3256af08f589f285bde37d8c628eafd752/protocol-core-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>