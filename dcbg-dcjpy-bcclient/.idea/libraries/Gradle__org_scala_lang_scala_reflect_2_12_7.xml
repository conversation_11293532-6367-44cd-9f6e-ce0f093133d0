<component name="libraryTable">
  <library name="Gradle: org.scala-lang:scala-reflect:2.12.7" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.scala-lang" artifactId="scala-reflect" version="2.12.7" baseVersion="2.12.7" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.scala-lang/scala-reflect/2.12.7/c5a8eb12969e77db4c0dd785c104b59d226b8265/scala-reflect-2.12.7.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.scala-lang/scala-reflect/2.12.7/45a6a316dd557b976cf72f47dc799069fcfb3b76/scala-reflect-2.12.7-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.scala-lang/scala-reflect/2.12.7/98c840d3cc35317bae945ee4e7a5960b47805edb/scala-reflect-2.12.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>