<component name="libraryTable">
  <library name="Gradle: org.jboss.logging:jboss-logging:3.4.3.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.jboss.logging" artifactId="jboss-logging" version="3.4.3.Final" baseVersion="3.4.3.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jboss.logging/jboss-logging/3.4.3.Final/c4bd7e12a745c0e7f6cf98c45cdcdf482fd827ea/jboss-logging-3.4.3.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jboss.logging/jboss-logging/3.4.3.Final/92d1ec42f87cf13cd074634ddf952ba76a272b66/jboss-logging-3.4.3.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jboss.logging/jboss-logging/3.4.3.Final/85e076a2f4d153c57f4dc414e7212a1b1e1a29fd/jboss-logging-3.4.3.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>