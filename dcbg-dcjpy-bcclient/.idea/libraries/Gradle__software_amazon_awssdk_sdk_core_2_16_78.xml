<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:sdk-core:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="sdk-core" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sdk-core/2.16.78/1d48be01d03ad461a9e7e0d81c75cab01003ec5/sdk-core-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sdk-core/2.16.78/2bb85eb482e2f6637dbe593b2ddbdec31cec80ae/sdk-core-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/sdk-core/2.16.78/4fefef225f64804d37ac56d62dd2f9a39324d333/sdk-core-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>