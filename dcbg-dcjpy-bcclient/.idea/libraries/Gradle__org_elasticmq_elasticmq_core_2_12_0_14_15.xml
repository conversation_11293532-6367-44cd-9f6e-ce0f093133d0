<component name="libraryTable">
  <library name="Gradle: org.elasticmq:elasticmq-core_2.12:0.14.15" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.elasticmq" artifactId="elasticmq-core_2.12" version="0.14.15" baseVersion="0.14.15" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-core_2.12/0.14.15/2030b3f7ca8e8bce2574bef57fd4a91420339f3a/elasticmq-core_2.12-0.14.15.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-core_2.12/0.14.15/b941a2059fcc1f35da7e1d43b52b60c894471431/elasticmq-core_2.12-0.14.15-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-core_2.12/0.14.15/685120a1192a77f441f820efcbdc73fa220817d0/elasticmq-core_2.12-0.14.15-sources.jar!/" />
    </SOURCES>
  </library>
</component>