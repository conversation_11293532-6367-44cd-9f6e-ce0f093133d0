<component name="libraryTable">
  <library name="Gradle: io.findify:s3mock_2.12:0.2.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.findify" artifactId="s3mock_2.12" version="0.2.6" baseVersion="0.2.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.findify/s3mock_2.12/0.2.6/a06e2f63a0ee1630f814eb384f94236f54c1b6c3/s3mock_2.12-0.2.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.findify/s3mock_2.12/0.2.6/45627bcc203027585729528803381cbbdbafed30/s3mock_2.12-0.2.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.findify/s3mock_2.12/0.2.6/d816209fe135743b12c086f7a77105f237c5fc69/s3mock_2.12-0.2.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>