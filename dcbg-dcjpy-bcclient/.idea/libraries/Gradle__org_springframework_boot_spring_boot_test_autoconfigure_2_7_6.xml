<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-test-autoconfigure:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-test-autoconfigure" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/2.7.6/44a1fa836bafe21f00775c92694722a9fe95a5f5/spring-boot-test-autoconfigure-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/2.7.6/9ecdb5c1c0078b43cd9445a2b27ed05497bbe1a8/spring-boot-test-autoconfigure-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/2.7.6/db144b464f4da3e537a145ef2f173d544f3e06b/spring-boot-test-autoconfigure-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>