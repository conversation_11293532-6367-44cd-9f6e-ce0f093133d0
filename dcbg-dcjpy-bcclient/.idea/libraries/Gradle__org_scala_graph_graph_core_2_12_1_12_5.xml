<component name="libraryTable">
  <library name="Gradle: org.scala-graph:graph-core_2.12:1.12.5" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.scala-graph" artifactId="graph-core_2.12" version="1.12.5" baseVersion="1.12.5" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.scala-graph/graph-core_2.12/1.12.5/45c78e9beddbe2a2dbf88dd1e1877132a3d3bcf7/graph-core_2.12-1.12.5.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.scala-graph/graph-core_2.12/1.12.5/8650e2ee72eba098cce7833f4867db88cc82b34a/graph-core_2.12-1.12.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.scala-graph/graph-core_2.12/1.12.5/ebe080a1c93310d3a355c8cbfdea70af7420c46b/graph-core_2.12-1.12.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>