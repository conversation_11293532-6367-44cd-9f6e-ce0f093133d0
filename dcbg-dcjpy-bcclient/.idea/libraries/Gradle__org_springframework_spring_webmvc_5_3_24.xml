<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-webmvc:5.3.24" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-webmvc" version="5.3.24" baseVersion="5.3.24" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webmvc/5.3.24/33d2187c2bf1cb2c222bd1cc18b618736babcf3d/spring-webmvc-5.3.24.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webmvc/5.3.24/7e838f36d4a3d35e1c59d816c79bd70c22a58a9/spring-webmvc-5.3.24-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webmvc/5.3.24/49295d337903c04f00c75cc87b4ebc1cb911dd24/spring-webmvc-5.3.24-sources.jar!/" />
    </SOURCES>
  </library>
</component>