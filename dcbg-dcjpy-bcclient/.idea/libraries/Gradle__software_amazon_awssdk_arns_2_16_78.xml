<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:arns:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="arns" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/arns/2.16.78/2f11b8c39cc0b319673ff2795636a41562072f1f/arns-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/arns/2.16.78/e174ecada03a6b71c5edad1c8ad10f265be17ba7/arns-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/arns/2.16.78/9104d3260bc8392a0db805904bfdd0be2e74570e/arns-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>