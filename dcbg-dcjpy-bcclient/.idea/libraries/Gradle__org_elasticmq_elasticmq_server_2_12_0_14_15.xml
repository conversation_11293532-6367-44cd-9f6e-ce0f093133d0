<component name="libraryTable">
  <library name="Gradle: org.elasticmq:elasticmq-server_2.12:0.14.15" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.elasticmq" artifactId="elasticmq-server_2.12" version="0.14.15" baseVersion="0.14.15" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-server_2.12/0.14.15/6d450539557a235245cc70c85067380fa0796d56/elasticmq-server_2.12-0.14.15.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-server_2.12/0.14.15/5690edb5098df90e04ea0d33fe12880598d6f7ec/elasticmq-server_2.12-0.14.15-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.elasticmq/elasticmq-server_2.12/0.14.15/c1ec372815cbd099f28c3ac4b3e889d4022c19d5/elasticmq-server_2.12-0.14.15-sources.jar!/" />
    </SOURCES>
  </library>
</component>