<component name="libraryTable">
  <library name="Gradle: com.typesafe.scala-logging:scala-logging_2.12:3.9.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe.scala-logging" artifactId="scala-logging_2.12" version="3.9.2" baseVersion="3.9.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.scala-logging/scala-logging_2.12/3.9.2/b1f19bc6774e01debf09bf5f564ad3613687bf49/scala-logging_2.12-3.9.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.scala-logging/scala-logging_2.12/3.9.2/e1013d7e5130e147f06a3a5cac958b7b6dc82fd7/scala-logging_2.12-3.9.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.scala-logging/scala-logging_2.12/3.9.2/811d21a0165b722a8f18d46b198f8f958105815c/scala-logging_2.12-3.9.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>