<component name="libraryTable">
  <library name="Gradle: net.minidev:json-smart:2.4.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="net.minidev" artifactId="json-smart" version="2.4.8" baseVersion="2.4.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/json-smart/2.4.8/7c62f5f72ab05eb54d40e2abf0360a2fe9ea477f/json-smart-2.4.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/json-smart/2.4.8/2071d8ff594b1fba697c26e255021a850d8c1612/json-smart-2.4.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/json-smart/2.4.8/399ee58414b3686be3d4f3b59f6bc11d30d2309e/json-smart-2.4.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>