<component name="libraryTable">
  <library name="Gradle: com.amazonaws:aws-java-sdk-core:1.11.294" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.amazonaws" artifactId="aws-java-sdk-core" version="1.11.294" baseVersion="1.11.294" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.amazonaws/aws-java-sdk-core/1.11.294/492358866f03b426f4f34738b224e897f2680af1/aws-java-sdk-core-1.11.294.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.amazonaws/aws-java-sdk-core/1.11.294/b307d8d55464a7f75da3ab900b638d037e94a2da/aws-java-sdk-core-1.11.294-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.amazonaws/aws-java-sdk-core/1.11.294/79009c0e93af80d5548badd273ea203b3e7a0fe9/aws-java-sdk-core-1.11.294-sources.jar!/" />
    </SOURCES>
  </library>
</component>