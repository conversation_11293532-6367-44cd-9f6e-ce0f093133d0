<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-junit-jupiter:4.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.mockito" artifactId="mockito-junit-jupiter" version="4.5.1" baseVersion="4.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/4.5.1/f81fb60bd69b3a6e5537ae23b883326f01632a61/mockito-junit-jupiter-4.5.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/4.5.1/5a31b8e0389e9fa4011078541fbd6870e99145b4/mockito-junit-jupiter-4.5.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/4.5.1/6aa189452f42167be3dc9ce5ac029df0f15ba0e5/mockito-junit-jupiter-4.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>