<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/2.7.6/d15341c0fc0b1d1362ce6c737b34679ad1f836c/spring-boot-starter-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/2.7.6/1911c059ab200d14bbcce50ce00b1cf6290f1501/spring-boot-starter-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/2.7.6/5f722ea7160c89f28b4bb20da2204ccfc912a6a/spring-boot-starter-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>