<component name="libraryTable">
  <library name="Gradle: org.junit.platform:junit-platform-commons:1.8.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.platform" artifactId="junit-platform-commons" version="1.8.2" baseVersion="1.8.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-commons/1.8.2/32c8b8617c1342376fd5af2053da6410d8866861/junit-platform-commons-1.8.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-commons/1.8.2/7772be92638dac16b6cf3ff05acd715d9d599608/junit-platform-commons-1.8.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.platform/junit-platform-commons/1.8.2/386b61876dd33d0ef0381189d3c1db9455d99659/junit-platform-commons-1.8.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>