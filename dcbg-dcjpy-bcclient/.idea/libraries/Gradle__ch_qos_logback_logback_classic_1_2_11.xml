<component name="libraryTable">
  <library name="Gradle: ch.qos.logback:logback-classic:1.2.11" type="java-imported" external-system-id="GRADLE">
    <properties groupId="ch.qos.logback" artifactId="logback-classic" version="1.2.11" baseVersion="1.2.11" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.2.11/4741689214e9d1e8408b206506cbe76d1c6a7d60/logback-classic-1.2.11.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.2.11/4e22f922862cd44d9741ff13fa88c1b9ddee96a/logback-classic-1.2.11-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.2.11/c6d3537d4af04b787f2fc15c52215df03ce66098/logback-classic-1.2.11-sources.jar!/" />
    </SOURCES>
  </library>
</component>