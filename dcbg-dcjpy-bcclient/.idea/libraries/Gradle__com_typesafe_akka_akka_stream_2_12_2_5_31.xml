<component name="libraryTable">
  <library name="Gradle: com.typesafe.akka:akka-stream_2.12:2.5.31" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.typesafe.akka" artifactId="akka-stream_2.12" version="2.5.31" baseVersion="2.5.31" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-stream_2.12/2.5.31/a58e997bd6c394360a2674daa95765ac9ef5a79b/akka-stream_2.12-2.5.31.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-stream_2.12/2.5.31/4c5f0e04318b49d50cdd7034d0eae5570386e388/akka-stream_2.12-2.5.31-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.typesafe.akka/akka-stream_2.12/2.5.31/5d98e3f35ddaa855df773d5022a1ff6f5fdc8f8e/akka-stream_2.12-2.5.31-sources.jar!/" />
    </SOURCES>
  </library>
</component>