<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:aws-json-protocol:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="aws-json-protocol" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-json-protocol/2.16.78/ece8c1ccd447c1fe73a887fe8c48cedbbc9c441b/aws-json-protocol-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-json-protocol/2.16.78/e5c6766705ac000aec7224e6ff4426abc0a744d1/aws-json-protocol-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/aws-json-protocol/2.16.78/272158640f6a0bcdb8df359d00d1de638c9d7376/aws-json-protocol-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>