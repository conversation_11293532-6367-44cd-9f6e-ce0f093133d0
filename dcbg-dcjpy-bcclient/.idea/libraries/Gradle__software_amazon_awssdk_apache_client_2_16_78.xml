<component name="libraryTable">
  <library name="Gradle: software.amazon.awssdk:apache-client:2.16.78" type="java-imported" external-system-id="GRADLE">
    <properties groupId="software.amazon.awssdk" artifactId="apache-client" version="2.16.78" baseVersion="2.16.78" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/apache-client/2.16.78/23a1480c6a5654f8706d58056b526b19e01b6f42/apache-client-2.16.78.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/apache-client/2.16.78/252a147a36beac35c4c5acea786e672883e44906/apache-client-2.16.78-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/apache-client/2.16.78/6184368f68d93bd6253e6384168f8268073dcdbc/apache-client-2.16.78-sources.jar!/" />
    </SOURCES>
  </library>
</component>