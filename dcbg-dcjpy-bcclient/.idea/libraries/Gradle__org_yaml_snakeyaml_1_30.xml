<component name="libraryTable">
  <library name="Gradle: org.yaml:snakeyaml:1.30" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.yaml" artifactId="snakeyaml" version="1.30" baseVersion="1.30" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.30/8fde7fe2586328ac3c68db92045e1c8759125000/snakeyaml-1.30.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.30/9445bc81714b2844c7caffe2e2597e67ede7a68f/snakeyaml-1.30-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.30/2fa17b2e1e6edc10ce8dec15df18a844efd8318b/snakeyaml-1.30-sources.jar!/" />
    </SOURCES>
  </library>
</component>