<component name="libraryTable">
  <library name="Gradle: com.github.jnr:jnr-x86asm:1.0.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.github.jnr" artifactId="jnr-x86asm" version="1.0.2" baseVersion="1.0.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-x86asm/1.0.2/6936bbd6c5b235665d87bd450f5e13b52d4b48/jnr-x86asm-1.0.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-x86asm/1.0.2/65aa28fb4f4bf735e3cb657a33f82f3b4388b99a/jnr-x86asm-1.0.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.jnr/jnr-x86asm/1.0.2/6bb6efcd010567ad80c8bb2bdd5065b30f0f1468/jnr-x86asm-1.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>