<component name="libraryTable">
  <library name="Gradle: org.projectlombok:lombok:1.18.24" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.projectlombok" artifactId="lombok" version="1.18.24" baseVersion="1.18.24" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.24/13a394eed5c4f9efb2a6d956e2086f1d81e857d9/lombok-1.18.24.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.24/9840a823a9fa5c5a67e595f74c23374923bd393e/lombok-1.18.24-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.24/a95c9f4bc8e3000f7b7adfdf5732b13cca5451e/lombok-1.18.24-sources.jar!/" />
    </SOURCES>
  </library>
</component>