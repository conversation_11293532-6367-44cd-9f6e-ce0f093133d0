<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-json:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-json" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-json/2.7.6/77017e2aa78224c3fa10444d659c2ce8765b910/spring-boot-starter-json-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-json/2.7.6/a633681e71f9e8027018f992ad6264e30e2c2d77/spring-boot-starter-json-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-json/2.7.6/a7cd8337d410f388c3d048fb4ef0f09c74d8e80/spring-boot-starter-json-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>