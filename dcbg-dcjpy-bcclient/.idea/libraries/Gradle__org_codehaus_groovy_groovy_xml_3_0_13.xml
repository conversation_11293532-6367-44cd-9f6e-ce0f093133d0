<component name="libraryTable">
  <library name="Gradle: org.codehaus.groovy:groovy-xml:3.0.13" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.codehaus.groovy" artifactId="groovy-xml" version="3.0.13" baseVersion="3.0.13" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-xml/3.0.13/ddfcf27160748971f2d3aafc00ede067da2f5ee9/groovy-xml-3.0.13.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-xml/3.0.13/361b787c1c6e8ad6f37a5d9a974447af3dc35926/groovy-xml-3.0.13-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.groovy/groovy-xml/3.0.13/97df3ca0505ca9531ad2a637198c01226629538e/groovy-xml-3.0.13-sources.jar!/" />
    </SOURCES>
  </library>
</component>