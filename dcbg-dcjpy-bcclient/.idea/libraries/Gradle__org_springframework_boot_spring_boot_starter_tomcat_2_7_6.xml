<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-tomcat:2.7.6" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-tomcat" version="2.7.6" baseVersion="2.7.6" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-tomcat/2.7.6/ec920cd7f951f3cff3a0b7af80bb8f7116a3d8a5/spring-boot-starter-tomcat-2.7.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-tomcat/2.7.6/1030074c1eab3da1b13a93dd5b1c2b92bc33d2dd/spring-boot-starter-tomcat-2.7.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-tomcat/2.7.6/fec1fe618c3008189402540a341b97a94f463da5/spring-boot-starter-tomcat-2.7.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>