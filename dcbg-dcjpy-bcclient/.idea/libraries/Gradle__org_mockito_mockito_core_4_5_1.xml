<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-core:4.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.mockito" artifactId="mockito-core" version="4.5.1" baseVersion="4.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/4.5.1/ed456e623e5afc6f4cee3ae58144e5c45f3b3bf/mockito-core-4.5.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/4.5.1/db32ce8390caa346d527cec3358a6cb40e557732/mockito-core-4.5.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/4.5.1/c08b438bd19ba98419cf7919ba63e909f75b71c/mockito-core-4.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>