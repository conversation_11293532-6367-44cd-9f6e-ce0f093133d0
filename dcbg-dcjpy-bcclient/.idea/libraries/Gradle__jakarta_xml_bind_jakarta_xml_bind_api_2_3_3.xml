<component name="libraryTable">
  <library name="Gradle: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.xml.bind" artifactId="jakarta.xml.bind-api" version="2.3.3" baseVersion="2.3.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/2.3.3/48e3b9cfc10752fba3521d6511f4165bea951801/jakarta.xml.bind-api-2.3.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/2.3.3/5685a1e75f9135dcc71e73a690b378e90fcd3b53/jakarta.xml.bind-api-2.3.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/2.3.3/d83744bae211a4072c39f007000a13f501a88395/jakarta.xml.bind-api-2.3.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>