#!/bin/bash

ROOT=$(cd $(dirname $BASH_SOURCE)/..; pwd)
pushd $ROOT > /dev/null

if [ $# -eq 1 ] && [ "$1" = "local" ] ; then
  aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin "${ECR_REGISTRY}" || exit 1
fi

chmod +x gradlew
./gradlew clean build || exit 1

IMAGE_TAG=$(git rev-parse --short HEAD)

docker build -t "$ECR_REGISTRY"/"$ECR_REPOSITORY":"$IMAGE_TAG" . --platform linux/amd64
docker push "$ECR_REGISTRY"/"$ECR_REPOSITORY":"$IMAGE_TAG" || exit 1