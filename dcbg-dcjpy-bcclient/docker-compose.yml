version: '3.8'
services:
  app:
    image: amazoncorretto:17
    ports:
      - 8081:8081
    environment:
      - SPRING_PROFILES_ACTIVE=local
    working_dir: /app
    volumes:
      - .:/app
    entrypoint: bash
    command: ./gradlew bootRun
  minio:
    image: minio/minio:RELEASE.2021-04-06T23-11-00Z.hotfix.f3cd60697
    ports:
      - 9000:9000
    environment:
      - MINIO_ACCESS_KEY=access123
      - MINIO_SECRET_KEY=secret123
    entrypoint: sh
    command: -c "
      rm -rf /data/*;
      mkdir -p /data/.minio.sys/buckets;
      cp -r /policies/* /data/.minio.sys/;
      cp -r /export/* /data/;
      /usr/bin/minio server /data;
      "
    volumes:
      - ./docker/minio/data:/data
      - ./docker/minio/export:/export
      - ./docker/minio/config:/root/.minio
      - ./docker/minio/policies:/policies
  minio-external:
    image: minio/minio:RELEASE.2021-04-06T23-11-00Z.hotfix.f3cd60697
    ports:
      - 9001:9000
    environment:
      - MINIO_ACCESS_KEY=access123
      - MINIO_SECRET_KEY=secret123
    entrypoint: sh
    command: -c "
      rm -rf /data/*;
      mkdir -p /data/.minio.sys/buckets;
      cp -r /policies/* /data/.minio.sys/;
      cp -r /export/* /data/;
      /usr/bin/minio server /data;
      "
    volumes:
      - ./docker/minio-external/data:/data
      - ./docker/minio-external/export:/export
      - ./docker/minio-external/config:/root/.minio
      - ./docker/minio-external/policies:/policies
  sqs:
    image: softwaremill/elasticmq-native:1.2.2
    ports:
      - 9324:9324
      - 9325:9325
    volumes:
      - ./docker/elasticmq/elasticmq.conf:/opt/elasticmq.conf
