name: "Continuous Deployment"
run-name: "[${{ github.event.inputs.environment }}][${{github.ref_name}}] Continuous Deployment"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
env:
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}

jobs:
  build-and-push:
    
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    timeout-minutes: 20

    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          role-duration-seconds: 1800

      - name: Amazon ECR Login
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build with Gradle / Docker Build and Push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}
        run: ./scripts/build_and_ecr_push.sh

      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BCClient] build-and-push job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: <!channel> Successfully executed the build-and-push job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BCClient] build-and-push job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The build-and-push job failed to execute.

  update-manifests:
    runs-on: ubuntu-latest
    needs: build-and-push
    timeout-minutes: 20
    env:
      MANIFEST_REPOSITORY: decurret-lab/dcbg-dcf-kubernetes-sandbox

    steps:
      - name: set image_tag environment value
        run:
          echo "image_tag=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV

      - name: dispatch update-manifests
        uses: peter-evans/repository-dispatch@v2
        with:
          repository: ${{ env.MANIFEST_REPOSITORY }}
          token: ${{ secrets.ACCESS_TOKEN_FOR_GITOPS }}
          event-type: update-manifest-bcclient
          client-payload: '{"sha": "${{ env.image_tag }}", "env": "${{ github.event.inputs.environment }}"}'

      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BCClient] update-manifests job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: <!channel> Successfully executed the update-manifests job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BCClient] update-manifests job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The update-manifests job failed to execute.
