include classpath("application.conf")

node-address {
   protocol = http
   host = "*"
   port = 9326
   context-path = ""
}

rest-sqs {
   enabled = true
   bind-port = 9326
   bind-hostname = "0.0.0.0"
   // relaxed or strict
   sqs-limits = strict
}

queues {
   "test-queue.fifo" {
     fifo = true
     defaultVisibilityTimeout = 30 seconds
     delay = 0 seconds
     receiveMessageWait = 0 seconds
   }
}
