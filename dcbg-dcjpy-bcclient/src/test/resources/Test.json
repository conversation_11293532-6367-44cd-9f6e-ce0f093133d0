{"contractName": "test", "abi": [{"inputs": [], "name": "getArray", "outputs": [{"internalType": "uint256[]", "name": "someUint", "type": "uint256[]"}, {"internalType": "bytes32[]", "name": "someByte", "type": "bytes32[]"}, {"internalType": "bool[]", "name": "someBool", "type": "bool[]"}, {"internalType": "uint256", "name": "count", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}], "compiler": {"name": "solc", "version": "0.6.12+commit.27d51765.Emscripten.clang"}, "networks": {"5157": {"events": {"0xc9bd4d6d4551cbbd492db819e98c425ec08809a4c1da651f4a70ac8cc1aad2a9": {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tupleDataId", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "someByte", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "someUint", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "someBool", "type": "bool"}], "name": "TupleTypes", "type": "event"}}, "links": {}, "address": "******************************************", "transactionHash": "0x287191c329d6aeb4546fb4c8991c77d62e13b2942e59f1a68ab3effee32aa140"}}, "schemaVersion": "3.3.3", "updatedAt": "2021-08-02T05:50:32.518Z", "networkType": "ethereum", "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}