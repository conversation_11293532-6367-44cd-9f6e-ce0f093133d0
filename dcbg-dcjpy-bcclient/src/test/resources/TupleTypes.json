{"contractName": "tupleTypes", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tupleDataId", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "someByte", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "someUint", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "someBool", "type": "bool"}], "name": "TupleTypes", "type": "event"}, {"inputs": [], "name": "getTupleTypes", "outputs": [{"components": [{"internalType": "bytes32", "name": "someByte", "type": "bytes32"}, {"internalType": "uint256", "name": "someUint", "type": "uint256"}, {"internalType": "bool", "name": "someBool", "type": "bool"}], "internalType": "struct tupleTypes.TupleData[]", "name": "tupleDataList", "type": "tuple[]"}], "stateMutability": "view", "type": "function", "constant": true}, {"inputs": [], "name": "getTupleIds", "outputs": [{"internalType": "uint256", "name": "count", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}, {"inputs": [{"internalType": "uint256", "name": "tupleId", "type": "uint256"}, {"internalType": "bytes32", "name": "someByte", "type": "bytes32"}, {"internalType": "uint256", "name": "someUint", "type": "uint256"}, {"internalType": "bool", "name": "someBool", "type": "bool"}], "name": "setTupleTypes", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.6.12+commit.27d51765\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tupleDataId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"someByte\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"someUint\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"someBool\",\"type\":\"bool\"}],\"name\":\"TupleTypes\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"getTupleIds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"count\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTupleTypes\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"someByte\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"someUint\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"someBool\",\"type\":\"bool\"}],\"internalType\":\"struct tupleTypes.TupleData[]\",\"name\":\"tupleDataList\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tupleId\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"someByte\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"someUint\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"someBool\",\"type\":\"bool\"}],\"name\":\"setTupleTypes\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"/Users/<USER>/git/dcbg-dcf-contract-prac/tupleTypes/contracts/tupleTypes.sol\":\"tupleTypes\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"/Users/<USER>/git/dcbg-dcf-contract-prac/tupleTypes/contracts/tupleTypes.sol\":{\"keccak256\":\"0x581986f3a2b4765eb41dae345ab240488a6039f631d8cb39bbe5498e5300ddc9\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://b97170a953fa4f33296e159e847b3c57d656eb8bb1ad7aadad3c81b52a5e7171\",\"dweb:/ipfs/QmTaAzJcZj2MTXbWLtvR1oYxfQHyip3g6Cj1T28UtQ9yqU\"]}},\"version\":1}", "bytecode": "0x608060405234801561001057600080fd5b506103a5806100206000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c80630f9a857d146100465780634e9b5656146100645780635fd378ff14610079575b600080fd5b61004e61008e565b60405161005b91906102f3565b60405180910390f35b61006c6101ef565b60405161005b9190610366565b61008c6100873660046102af565b6101f5565b005b600054606090818167ffffffffffffffff811180156100ac57600080fd5b506040519080825280602002602001820160405280156100e657816020015b6100d361028f565b8152602001906001900390816100cb5790505b50905060005b828110156101e8576001600080838154811061010457fe5b906000526020600020015481526020019081526020016000206000015482828151811061012d57fe5b602002602001015160000181815250506001600080838154811061014d57fe5b906000526020600020015481526020019081526020016000206001015482828151811061017657fe5b602002602001015160200181815250506001600080838154811061019657fe5b9060005260206000200154815260200190815260200160002060020160009054906101000a900460ff168282815181106101cc57fe5b60209081029190910101519015156040909101526001016100ec565b5091505090565b60005490565b60008054600180820183557f290decd9548b62a8d60345a988386fc84ba6bc95484008f6362f93160ef3e5639091018690558582526020819052604091829020858155908101849055600201805460ff19168315151790555184907fc9bd4d6d4551cbbd492db819e98c425ec08809a4c1da651f4a70ac8cc1aad2a9906102819086908690869061034e565b60405180910390a250505050565b604080516060810182526000808252602082018190529181019190915290565b600080600080608085870312156102c4578384fd5b843593506020850135925060408501359150606085013580151581146102e8578182fd5b939692955090935050565b602080825282518282018190526000919060409081850190868401855b8281101561034157815180518552868101518786015285015115158585015260609093019290850190600101610310565b5091979650505050505050565b92835260208301919091521515604082015260600190565b9081526020019056fea2646970667358221220229045c06619f459ab76951a8f1722c79c3c8589fbe6d5e5ecb7a4b221f84e3764736f6c634300060c0033", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106100415760003560e01c80630f9a857d146100465780634e9b5656146100645780635fd378ff14610079575b600080fd5b61004e61008e565b60405161005b91906102f3565b60405180910390f35b61006c6101ef565b60405161005b9190610366565b61008c6100873660046102af565b6101f5565b005b600054606090818167ffffffffffffffff811180156100ac57600080fd5b506040519080825280602002602001820160405280156100e657816020015b6100d361028f565b8152602001906001900390816100cb5790505b50905060005b828110156101e8576001600080838154811061010457fe5b906000526020600020015481526020019081526020016000206000015482828151811061012d57fe5b602002602001015160000181815250506001600080838154811061014d57fe5b906000526020600020015481526020019081526020016000206001015482828151811061017657fe5b602002602001015160200181815250506001600080838154811061019657fe5b9060005260206000200154815260200190815260200160002060020160009054906101000a900460ff168282815181106101cc57fe5b60209081029190910101519015156040909101526001016100ec565b5091505090565b60005490565b60008054600180820183557f290decd9548b62a8d60345a988386fc84ba6bc95484008f6362f93160ef3e5639091018690558582526020819052604091829020858155908101849055600201805460ff19168315151790555184907fc9bd4d6d4551cbbd492db819e98c425ec08809a4c1da651f4a70ac8cc1aad2a9906102819086908690869061034e565b60405180910390a250505050565b604080516060810182526000808252602082018190529181019190915290565b600080600080608085870312156102c4578384fd5b843593506020850135925060408501359150606085013580151581146102e8578182fd5b939692955090935050565b602080825282518282018190526000919060409081850190868401855b8281101561034157815180518552868101518786015285015115158585015260609093019290850190600101610310565b5091979650505050505050565b92835260208301919091521515604082015260600190565b9081526020019056fea2646970667358221220229045c06619f459ab76951a8f1722c79c3c8589fbe6d5e5ecb7a4b221f84e3764736f6c634300060c0033", "immutableReferences": {}, "sourceMap": "98:1292:1:-:0;;;;;;;;;;;;;;;;;;;", "deployedSourceMap": "98:1292:1:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;424:490;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;918:99;;;:::i;:::-;;;;;;;:::i;1021:367::-;;;;;;:::i;:::-;;:::i;:::-;;424:490;526:12;541:20;484:32;;;541:20;598:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;567:52;;631:9;626:261;650:4;646:1;:8;626:261;;;693:14;:32;708:13;722:1;708:16;;;;;;;;;;;;;;;;693:32;;;;;;;;;;;:41;;;669:9;679:1;669:12;;;;;;;;;;;;;;:21;;:65;;;;;766:14;:32;781:13;795:1;781:16;;;;;;;;;;;;;;;;766:32;;;;;;;;;;;:41;;;742:9;752:1;742:12;;;;;;;;;;;;;;:21;;:65;;;;;839:14;:32;854:13;868:1;854:16;;;;;;;;;;;;;;;;839:32;;;;;;;;;;;:41;;;;;;;;;;;;815:9;825:1;815:12;;;;;;;;;;;;;;;;;;:65;;;:21;;;;:65;656:3;;626:261;;;-1:-1:-1;900:9:1;-1:-1:-1;;424:490:1;:::o;918:99::-;964:13;992:20;918:99;:::o;1021:367::-;1147:13;:27;;;;;;;;;;;;;;;1181:23;;;1147:27;1181:23;;;;;;;;:43;;;1230:32;;;:43;;;1279:32;;:43;;-1:-1:-1;;1279:43:1;;;;;;;1334:49;1147:27;;1334:49;;;;1181:43;;1230;;1279;;1334:49;:::i;:::-;;;;;;;;1021:367;;;;:::o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;410:611::-;;;;;562:3;550:9;541:7;537:23;533:33;530:2;;;-1:-1;;569:12;530:2;353:6;340:20;621:63;;721:2;764:9;760:22;203:20;729:63;;829:2;872:9;868:22;340:20;837:63;;937:2;977:9;973:22;69:20;5829:5;5564:13;5557:21;5807:5;5804:32;5794:2;;-1:-1;;5840:12;5794:2;524:497;;;;-1:-1;524:497;;-1:-1;;524:497::o;3649:470::-;3876:2;3890:47;;;5105:12;;3861:18;;;5430:19;;;3649:470;;3876:2;5470:14;;;;;;4934;;;3649:470;1914:335;1939:6;1936:1;1933:13;1914:335;;;2000:13;;2989:23;;2552:37;;3153:16;;;3147:23;3224:14;;;2552:37;3311:16;;3305:23;5564:13;5557:21;3376:14;;;2334:34;1291:4;1282:14;;;;5260;;;;1961:1;1954:9;1914:335;;;-1:-1;3943:166;;3847:272;-1:-1;;;;;;;3847:272::o;4126:432::-;2552:37;;;4467:2;4452:18;;2552:37;;;;5564:13;5557:21;4544:2;4529:18;;2334:34;4303:2;4288:18;;4274:284::o;4565:222::-;2552:37;;;4692:2;4677:18;;4663:124::o", "source": "// SPDX-License-Identifier: UNLICENSED\npragma solidity ^0.6.0;\npragma experimental ABIEncoderV2;\n\ncontract tupleTypes {\n  struct TupleData {\n    bytes32 someByte;\n    uint256 someUint;\n    bool someBool;\n  }\n\n  uint256[] private _tupleDataIds;\n  mapping(uint256 => TupleData) private _tupleDataList;\n\n  event TupleTypes(\n    uint256 indexed tupleDataId,\n    bytes32 someByte,\n    uint256 someUint,\n    bool someBool\n  );\n\n  function getTupleTypes()\n    external\n    view\n    returns (TupleData[] memory tupleDataList)\n  {\n    uint256 size = _tupleDataIds.length;\n    TupleData[] memory tupleData = new TupleData[](size);\n\n    for (uint256 i = 0; i < size; i++) {\n      tupleData[i].someByte = _tupleDataList[_tupleDataIds[i]].someByte;\n      tupleData[i].someUint = _tupleDataList[_tupleDataIds[i]].someUint;\n      tupleData[i].someBool = _tupleDataList[_tupleDataIds[i]].someBool;\n    }\n\n    return tupleData;\n  }\n\n  function getTupleIds() external view returns (uint256 count) {\n    return _tupleDataIds.length;\n  }\n\n  function setTupleTypes(\n    uint256 tupleId,\n    bytes32 someByte,\n    uint256 someUint,\n    bool someBool\n  ) external {\n    _tupleDataIds.push(tupleId);\n\n    _tupleDataList[tupleId].someByte = someByte;\n    _tupleDataList[tupleId].someUint = someUint;\n    _tupleDataList[tupleId].someBool = someBool;\n\n    emit TupleTypes(tupleId, someByte, someUint, someBool);\n  }\n}\n", "sourcePath": "/Users/<USER>/git/dcbg-dcf-contract-prac/tupleTypes/contracts/tupleTypes.sol", "ast": {"absolutePath": "/Users/<USER>/git/dcbg-dcf-contract-prac/tupleTypes/contracts/tupleTypes.sol", "exportedSymbols": {"tupleTypes": [211]}, "id": 212, "license": "UNLICENSED", "nodeType": "SourceUnit", "nodes": [{"id": 58, "literals": ["solidity", "^", "0.6", ".0"], "nodeType": "PragmaDirective", "src": "39:23:1"}, {"id": 59, "literals": ["experimental", "ABIEncoderV2"], "nodeType": "PragmaDirective", "src": "63:33:1"}, {"abstract": false, "baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 211, "linearizedBaseContracts": [211], "name": "tupleTypes", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "tupleTypes.TupleData", "id": 66, "members": [{"constant": false, "id": 61, "mutability": "mutable", "name": "someByte", "nodeType": "VariableDeclaration", "overrides": null, "scope": 66, "src": "145:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 60, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "145:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 63, "mutability": "mutable", "name": "someUint", "nodeType": "VariableDeclaration", "overrides": null, "scope": 66, "src": "167:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 62, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "167:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 65, "mutability": "mutable", "name": "someBool", "nodeType": "VariableDeclaration", "overrides": null, "scope": 66, "src": "189:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 64, "name": "bool", "nodeType": "ElementaryTypeName", "src": "189:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "name": "TupleData", "nodeType": "StructDefinition", "scope": 211, "src": "122:85:1", "visibility": "public"}, {"constant": false, "id": 69, "mutability": "mutable", "name": "_tupleDataIds", "nodeType": "VariableDeclaration", "overrides": null, "scope": 211, "src": "211:31:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 67, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "211:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 68, "length": null, "nodeType": "ArrayTypeName", "src": "211:9:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "value": null, "visibility": "private"}, {"constant": false, "id": 73, "mutability": "mutable", "name": "_tupleDataList", "nodeType": "VariableDeclaration", "overrides": null, "scope": 211, "src": "246:52:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData)"}, "typeName": {"id": 72, "keyType": {"id": 70, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "254:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "246:29:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData)"}, "valueType": {"contractScope": null, "id": 71, "name": "TupleData", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 66, "src": "265:9:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage_ptr", "typeString": "struct tupleTypes.TupleData"}}}, "value": null, "visibility": "private"}, {"anonymous": false, "documentation": null, "id": 83, "name": "TupleTypes", "nodeType": "EventDefinition", "parameters": {"id": 82, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 75, "indexed": true, "mutability": "mutable", "name": "tupleDataId", "nodeType": "VariableDeclaration", "overrides": null, "scope": 83, "src": "325:27:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 74, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "325:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 77, "indexed": false, "mutability": "mutable", "name": "someByte", "nodeType": "VariableDeclaration", "overrides": null, "scope": 83, "src": "358:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 76, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "358:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 79, "indexed": false, "mutability": "mutable", "name": "someUint", "nodeType": "VariableDeclaration", "overrides": null, "scope": 83, "src": "380:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 78, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "380:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 81, "indexed": false, "mutability": "mutable", "name": "someBool", "nodeType": "VariableDeclaration", "overrides": null, "scope": 83, "src": "402:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 80, "name": "bool", "nodeType": "ElementaryTypeName", "src": "402:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "319:100:1"}, "src": "303:117:1"}, {"body": {"id": 154, "nodeType": "Block", "src": "520:394:1", "statements": [{"assignments": [90], "declarations": [{"constant": false, "id": 90, "mutability": "mutable", "name": "size", "nodeType": "VariableDeclaration", "overrides": null, "scope": 154, "src": "526:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 89, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "526:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "id": 93, "initialValue": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 91, "name": "_tupleDataIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 69, "src": "541:13:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 92, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "541:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "526:35:1"}, {"assignments": [97], "declarations": [{"constant": false, "id": 97, "mutability": "mutable", "name": "tupleData", "nodeType": "VariableDeclaration", "overrides": null, "scope": 154, "src": "567:28:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData[]"}, "typeName": {"baseType": {"contractScope": null, "id": 95, "name": "TupleData", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 66, "src": "567:9:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage_ptr", "typeString": "struct tupleTypes.TupleData"}}, "id": 96, "length": null, "nodeType": "ArrayTypeName", "src": "567:11:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_storage_$dyn_storage_ptr", "typeString": "struct tupleTypes.TupleData[]"}}, "value": null, "visibility": "internal"}], "id": 103, "initialValue": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 101, "name": "size", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 90, "src": "614:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 100, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "598:15:1", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct tupleTypes.TupleData memory[] memory)"}, "typeName": {"baseType": {"contractScope": null, "id": 98, "name": "TupleData", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 66, "src": "602:9:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage_ptr", "typeString": "struct tupleTypes.TupleData"}}, "id": 99, "length": null, "nodeType": "ArrayTypeName", "src": "602:11:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_storage_$dyn_storage_ptr", "typeString": "struct tupleTypes.TupleData[]"}}}, "id": 102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "598:21:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "567:52:1"}, {"body": {"id": 150, "nodeType": "Block", "src": "661:226:1", "statements": [{"expression": {"argumentTypes": null, "id": 124, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 114, "name": "tupleData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97, "src": "669:9:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData memory[] memory"}}, "id": 116, "indexExpression": {"argumentTypes": null, "id": 115, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "679:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "669:12:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_memory_ptr", "typeString": "struct tupleTypes.TupleData memory"}}, "id": 117, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "someByte", "nodeType": "MemberAccess", "referencedDeclaration": 61, "src": "669:21:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 118, "name": "_tupleDataList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 73, "src": "693:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData storage ref)"}}, "id": 122, "indexExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 119, "name": "_tupleDataIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 69, "src": "708:13:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 121, "indexExpression": {"argumentTypes": null, "id": 120, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "722:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "708:16:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "693:32:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage", "typeString": "struct tupleTypes.TupleData storage ref"}}, "id": 123, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "someByte", "nodeType": "MemberAccess", "referencedDeclaration": 61, "src": "693:41:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "669:65:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 125, "nodeType": "ExpressionStatement", "src": "669:65:1"}, {"expression": {"argumentTypes": null, "id": 136, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 126, "name": "tupleData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97, "src": "742:9:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData memory[] memory"}}, "id": 128, "indexExpression": {"argumentTypes": null, "id": 127, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "752:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "742:12:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_memory_ptr", "typeString": "struct tupleTypes.TupleData memory"}}, "id": 129, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "someUint", "nodeType": "MemberAccess", "referencedDeclaration": 63, "src": "742:21:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 130, "name": "_tupleDataList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 73, "src": "766:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData storage ref)"}}, "id": 134, "indexExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 131, "name": "_tupleDataIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 69, "src": "781:13:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 133, "indexExpression": {"argumentTypes": null, "id": 132, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "795:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "781:16:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "766:32:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage", "typeString": "struct tupleTypes.TupleData storage ref"}}, "id": 135, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "someUint", "nodeType": "MemberAccess", "referencedDeclaration": 63, "src": "766:41:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "742:65:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 137, "nodeType": "ExpressionStatement", "src": "742:65:1"}, {"expression": {"argumentTypes": null, "id": 148, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 138, "name": "tupleData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97, "src": "815:9:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData memory[] memory"}}, "id": 140, "indexExpression": {"argumentTypes": null, "id": 139, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "825:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "815:12:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_memory_ptr", "typeString": "struct tupleTypes.TupleData memory"}}, "id": 141, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "someBool", "nodeType": "MemberAccess", "referencedDeclaration": 65, "src": "815:21:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 142, "name": "_tupleDataList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 73, "src": "839:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData storage ref)"}}, "id": 146, "indexExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 143, "name": "_tupleDataIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 69, "src": "854:13:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 145, "indexExpression": {"argumentTypes": null, "id": 144, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "868:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "854:16:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "839:32:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage", "typeString": "struct tupleTypes.TupleData storage ref"}}, "id": 147, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "someBool", "nodeType": "MemberAccess", "referencedDeclaration": 65, "src": "839:41:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "815:65:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 149, "nodeType": "ExpressionStatement", "src": "815:65:1"}]}, "condition": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 110, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 108, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "646:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"argumentTypes": null, "id": 109, "name": "size", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 90, "src": "650:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "646:8:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 151, "initializationExpression": {"assignments": [105], "declarations": [{"constant": false, "id": 105, "mutability": "mutable", "name": "i", "nodeType": "VariableDeclaration", "overrides": null, "scope": 151, "src": "631:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 104, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "631:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "id": 107, "initialValue": {"argumentTypes": null, "hexValue": "30", "id": 106, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "643:1:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "631:13:1"}, "loopExpression": {"expression": {"argumentTypes": null, "id": 112, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "656:3:1", "subExpression": {"argumentTypes": null, "id": 111, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "656:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 113, "nodeType": "ExpressionStatement", "src": "656:3:1"}, "nodeType": "ForStatement", "src": "626:261:1"}, {"expression": {"argumentTypes": null, "id": 152, "name": "tupleData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97, "src": "900:9:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData memory[] memory"}}, "functionReturnParameters": 88, "id": 153, "nodeType": "Return", "src": "893:16:1"}]}, "documentation": null, "functionSelector": "0f9a857d", "id": 155, "implemented": true, "kind": "function", "modifiers": [], "name": "getTupleTypes", "nodeType": "FunctionDefinition", "overrides": null, "parameters": {"id": 84, "nodeType": "ParameterList", "parameters": [], "src": "446:2:1"}, "returnParameters": {"id": 88, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87, "mutability": "mutable", "name": "tupleDataList", "nodeType": "VariableDeclaration", "overrides": null, "scope": 155, "src": "484:32:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_memory_ptr_$dyn_memory_ptr", "typeString": "struct tupleTypes.TupleData[]"}, "typeName": {"baseType": {"contractScope": null, "id": 85, "name": "TupleData", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 66, "src": "484:9:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage_ptr", "typeString": "struct tupleTypes.TupleData"}}, "id": 86, "length": null, "nodeType": "ArrayTypeName", "src": "484:11:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_TupleData_$66_storage_$dyn_storage_ptr", "typeString": "struct tupleTypes.TupleData[]"}}, "value": null, "visibility": "internal"}], "src": "483:34:1"}, "scope": 211, "src": "424:490:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 163, "nodeType": "Block", "src": "979:38:1", "statements": [{"expression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 160, "name": "_tupleDataIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 69, "src": "992:13:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 161, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "992:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 159, "id": 162, "nodeType": "Return", "src": "985:27:1"}]}, "documentation": null, "functionSelector": "4e9b5656", "id": 164, "implemented": true, "kind": "function", "modifiers": [], "name": "getTupleIds", "nodeType": "FunctionDefinition", "overrides": null, "parameters": {"id": 156, "nodeType": "ParameterList", "parameters": [], "src": "938:2:1"}, "returnParameters": {"id": 159, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 158, "mutability": "mutable", "name": "count", "nodeType": "VariableDeclaration", "overrides": null, "scope": 164, "src": "964:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 157, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "964:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "963:15:1"}, "scope": 211, "src": "918:99:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 209, "nodeType": "Block", "src": "1141:247:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 178, "name": "tupleId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1166:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 175, "name": "_tupleDataIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 69, "src": "1147:13:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 177, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "push", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1147:18:1", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1147:27:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 180, "nodeType": "ExpressionStatement", "src": "1147:27:1"}, {"expression": {"argumentTypes": null, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 181, "name": "_tupleDataList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 73, "src": "1181:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData storage ref)"}}, "id": 183, "indexExpression": {"argumentTypes": null, "id": 182, "name": "tupleId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1196:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1181:23:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage", "typeString": "struct tupleTypes.TupleData storage ref"}}, "id": 184, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "someByte", "nodeType": "MemberAccess", "referencedDeclaration": 61, "src": "1181:32:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 185, "name": "someByte", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "1216:8:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "1181:43:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 187, "nodeType": "ExpressionStatement", "src": "1181:43:1"}, {"expression": {"argumentTypes": null, "id": 193, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 188, "name": "_tupleDataList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 73, "src": "1230:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData storage ref)"}}, "id": 190, "indexExpression": {"argumentTypes": null, "id": 189, "name": "tupleId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1245:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1230:23:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage", "typeString": "struct tupleTypes.TupleData storage ref"}}, "id": 191, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "someUint", "nodeType": "MemberAccess", "referencedDeclaration": 63, "src": "1230:32:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 192, "name": "someUint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 170, "src": "1265:8:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1230:43:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 194, "nodeType": "ExpressionStatement", "src": "1230:43:1"}, {"expression": {"argumentTypes": null, "id": 200, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 195, "name": "_tupleDataList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 73, "src": "1279:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_TupleData_$66_storage_$", "typeString": "mapping(uint256 => struct tupleTypes.TupleData storage ref)"}}, "id": 197, "indexExpression": {"argumentTypes": null, "id": 196, "name": "tupleId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1294:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1279:23:1", "typeDescriptions": {"typeIdentifier": "t_struct$_TupleData_$66_storage", "typeString": "struct tupleTypes.TupleData storage ref"}}, "id": 198, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "someBool", "nodeType": "MemberAccess", "referencedDeclaration": 65, "src": "1279:32:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 199, "name": "someBool", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "1314:8:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "1279:43:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 201, "nodeType": "ExpressionStatement", "src": "1279:43:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 203, "name": "tupleId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1345:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"argumentTypes": null, "id": 204, "name": "someByte", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "1354:8:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"argumentTypes": null, "id": 205, "name": "someUint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 170, "src": "1364:8:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"argumentTypes": null, "id": 206, "name": "someBool", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "1374:8:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 202, "name": "TupleTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 83, "src": "1334:10:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_bytes32_$_t_uint256_$_t_bool_$returns$__$", "typeString": "function (uint256,bytes32,uint256,bool)"}}, "id": 207, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1334:49:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 208, "nodeType": "EmitStatement", "src": "1329:54:1"}]}, "documentation": null, "functionSelector": "5fd378ff", "id": 210, "implemented": true, "kind": "function", "modifiers": [], "name": "setTupleTypes", "nodeType": "FunctionDefinition", "overrides": null, "parameters": {"id": 173, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 166, "mutability": "mutable", "name": "tupleId", "nodeType": "VariableDeclaration", "overrides": null, "scope": 210, "src": "1049:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 165, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1049:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 168, "mutability": "mutable", "name": "someByte", "nodeType": "VariableDeclaration", "overrides": null, "scope": 210, "src": "1070:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 167, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1070:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 170, "mutability": "mutable", "name": "someUint", "nodeType": "VariableDeclaration", "overrides": null, "scope": 210, "src": "1092:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 169, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1092:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 172, "mutability": "mutable", "name": "someBool", "nodeType": "VariableDeclaration", "overrides": null, "scope": 210, "src": "1114:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 171, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1114:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1043:88:1"}, "returnParameters": {"id": 174, "nodeType": "ParameterList", "parameters": [], "src": "1141:0:1"}, "scope": 211, "src": "1021:367:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "scope": 212, "src": "98:1292:1"}], "src": "39:1352:1"}, "legacyAST": {"attributes": {"absolutePath": "/Users/<USER>/git/dcbg-dcf-contract-prac/tupleTypes/contracts/tupleTypes.sol", "exportedSymbols": {"tupleTypes": [211]}, "license": "UNLICENSED"}, "children": [{"attributes": {"literals": ["solidity", "^", "0.6", ".0"]}, "id": 58, "name": "PragmaDirective", "src": "39:23:1"}, {"attributes": {"literals": ["experimental", "ABIEncoderV2"]}, "id": 59, "name": "PragmaDirective", "src": "63:33:1"}, {"attributes": {"abstract": false, "baseContracts": [null], "contractDependencies": [null], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "linearizedBaseContracts": [211], "name": "tupleTypes", "scope": 212}, "children": [{"attributes": {"canonicalName": "tupleTypes.TupleData", "name": "TupleData", "scope": 211, "visibility": "public"}, "children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "someByte", "overrides": null, "scope": 66, "stateVariable": false, "storageLocation": "default", "type": "bytes32", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bytes32", "type": "bytes32"}, "id": 60, "name": "ElementaryTypeName", "src": "145:7:1"}], "id": 61, "name": "VariableDeclaration", "src": "145:16:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "someUint", "overrides": null, "scope": 66, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 62, "name": "ElementaryTypeName", "src": "167:7:1"}], "id": 63, "name": "VariableDeclaration", "src": "167:16:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "someBool", "overrides": null, "scope": 66, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 64, "name": "ElementaryTypeName", "src": "189:4:1"}], "id": 65, "name": "VariableDeclaration", "src": "189:13:1"}], "id": 66, "name": "StructDefinition", "src": "122:85:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "_tupleDataIds", "overrides": null, "scope": 211, "stateVariable": true, "storageLocation": "default", "type": "uint256[]", "value": null, "visibility": "private"}, "children": [{"attributes": {"length": null, "type": "uint256[]"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 67, "name": "ElementaryTypeName", "src": "211:7:1"}], "id": 68, "name": "ArrayTypeName", "src": "211:9:1"}], "id": 69, "name": "VariableDeclaration", "src": "211:31:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "_tupleDataList", "overrides": null, "scope": 211, "stateVariable": true, "storageLocation": "default", "type": "mapping(uint256 => struct tupleTypes.TupleData)", "value": null, "visibility": "private"}, "children": [{"attributes": {"type": "mapping(uint256 => struct tupleTypes.TupleData)"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 70, "name": "ElementaryTypeName", "src": "254:7:1"}, {"attributes": {"contractScope": null, "name": "TupleData", "referencedDeclaration": 66, "type": "struct tupleTypes.TupleData"}, "id": 71, "name": "UserDefinedTypeName", "src": "265:9:1"}], "id": 72, "name": "Mapping", "src": "246:29:1"}], "id": 73, "name": "VariableDeclaration", "src": "246:52:1"}, {"attributes": {"anonymous": false, "documentation": null, "name": "TupleTypes"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "mutability": "mutable", "name": "tupleDataId", "overrides": null, "scope": 83, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 74, "name": "ElementaryTypeName", "src": "325:7:1"}], "id": 75, "name": "VariableDeclaration", "src": "325:27:1"}, {"attributes": {"constant": false, "indexed": false, "mutability": "mutable", "name": "someByte", "overrides": null, "scope": 83, "stateVariable": false, "storageLocation": "default", "type": "bytes32", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bytes32", "type": "bytes32"}, "id": 76, "name": "ElementaryTypeName", "src": "358:7:1"}], "id": 77, "name": "VariableDeclaration", "src": "358:16:1"}, {"attributes": {"constant": false, "indexed": false, "mutability": "mutable", "name": "someUint", "overrides": null, "scope": 83, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 78, "name": "ElementaryTypeName", "src": "380:7:1"}], "id": 79, "name": "VariableDeclaration", "src": "380:16:1"}, {"attributes": {"constant": false, "indexed": false, "mutability": "mutable", "name": "someBool", "overrides": null, "scope": 83, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 80, "name": "ElementaryTypeName", "src": "402:4:1"}], "id": 81, "name": "VariableDeclaration", "src": "402:13:1"}], "id": 82, "name": "ParameterList", "src": "319:100:1"}], "id": 83, "name": "EventDefinition", "src": "303:117:1"}, {"attributes": {"documentation": null, "functionSelector": "0f9a857d", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getTupleTypes", "overrides": null, "scope": 211, "stateMutability": "view", "virtual": false, "visibility": "external"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 84, "name": "ParameterList", "src": "446:2:1"}, {"children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "tupleDataList", "overrides": null, "scope": 155, "stateVariable": false, "storageLocation": "memory", "type": "struct tupleTypes.TupleData[]", "value": null, "visibility": "internal"}, "children": [{"attributes": {"length": null, "type": "struct tupleTypes.TupleData[]"}, "children": [{"attributes": {"contractScope": null, "name": "TupleData", "referencedDeclaration": 66, "type": "struct tupleTypes.TupleData"}, "id": 85, "name": "UserDefinedTypeName", "src": "484:9:1"}], "id": 86, "name": "ArrayTypeName", "src": "484:11:1"}], "id": 87, "name": "VariableDeclaration", "src": "484:32:1"}], "id": 88, "name": "ParameterList", "src": "483:34:1"}, {"children": [{"attributes": {"assignments": [90]}, "children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "size", "overrides": null, "scope": 154, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 89, "name": "ElementaryTypeName", "src": "526:7:1"}], "id": 90, "name": "VariableDeclaration", "src": "526:12:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "length", "referencedDeclaration": null, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 69, "type": "uint256[] storage ref", "value": "_tupleDataIds"}, "id": 91, "name": "Identifier", "src": "541:13:1"}], "id": 92, "name": "MemberAccess", "src": "541:20:1"}], "id": 93, "name": "VariableDeclarationStatement", "src": "526:35:1"}, {"attributes": {"assignments": [97]}, "children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "tupleData", "overrides": null, "scope": 154, "stateVariable": false, "storageLocation": "memory", "type": "struct tupleTypes.TupleData[]", "value": null, "visibility": "internal"}, "children": [{"attributes": {"length": null, "type": "struct tupleTypes.TupleData[]"}, "children": [{"attributes": {"contractScope": null, "name": "TupleData", "referencedDeclaration": 66, "type": "struct tupleTypes.TupleData"}, "id": 95, "name": "UserDefinedTypeName", "src": "567:9:1"}], "id": 96, "name": "ArrayTypeName", "src": "567:11:1"}], "id": 97, "name": "VariableDeclaration", "src": "567:28:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "tryCall": false, "type": "struct tupleTypes.TupleData memory[] memory", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "type": "function (uint256) pure returns (struct tupleTypes.TupleData memory[] memory)"}, "children": [{"attributes": {"length": null, "type": "struct tupleTypes.TupleData[]"}, "children": [{"attributes": {"contractScope": null, "name": "TupleData", "referencedDeclaration": 66, "type": "struct tupleTypes.TupleData"}, "id": 98, "name": "UserDefinedTypeName", "src": "602:9:1"}], "id": 99, "name": "ArrayTypeName", "src": "602:11:1"}], "id": 100, "name": "NewExpression", "src": "598:15:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 90, "type": "uint256", "value": "size"}, "id": 101, "name": "Identifier", "src": "614:4:1"}], "id": 102, "name": "FunctionCall", "src": "598:21:1"}], "id": 103, "name": "VariableDeclarationStatement", "src": "567:52:1"}, {"children": [{"attributes": {"assignments": [105]}, "children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "i", "overrides": null, "scope": 151, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 104, "name": "ElementaryTypeName", "src": "631:7:1"}], "id": 105, "name": "VariableDeclaration", "src": "631:9:1"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 106, "name": "Literal", "src": "643:1:1"}], "id": 107, "name": "VariableDeclarationStatement", "src": "631:13:1"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 108, "name": "Identifier", "src": "646:1:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 90, "type": "uint256", "value": "size"}, "id": 109, "name": "Identifier", "src": "650:4:1"}], "id": 110, "name": "BinaryOperation", "src": "646:8:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "++", "prefix": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 111, "name": "Identifier", "src": "656:1:1"}], "id": 112, "name": "UnaryOperation", "src": "656:3:1"}], "id": 113, "name": "ExpressionStatement", "src": "656:3:1"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bytes32"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "someByte", "referencedDeclaration": 61, "type": "bytes32"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData memory"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 97, "type": "struct tupleTypes.TupleData memory[] memory", "value": "tupleData"}, "id": 114, "name": "Identifier", "src": "669:9:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 115, "name": "Identifier", "src": "679:1:1"}], "id": 116, "name": "IndexAccess", "src": "669:12:1"}], "id": 117, "name": "MemberAccess", "src": "669:21:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "member_name": "someByte", "referencedDeclaration": 61, "type": "bytes32"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 73, "type": "mapping(uint256 => struct tupleTypes.TupleData storage ref)", "value": "_tupleDataList"}, "id": 118, "name": "Identifier", "src": "693:14:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 69, "type": "uint256[] storage ref", "value": "_tupleDataIds"}, "id": 119, "name": "Identifier", "src": "708:13:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 120, "name": "Identifier", "src": "722:1:1"}], "id": 121, "name": "IndexAccess", "src": "708:16:1"}], "id": 122, "name": "IndexAccess", "src": "693:32:1"}], "id": 123, "name": "MemberAccess", "src": "693:41:1"}], "id": 124, "name": "Assignment", "src": "669:65:1"}], "id": 125, "name": "ExpressionStatement", "src": "669:65:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "someUint", "referencedDeclaration": 63, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData memory"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 97, "type": "struct tupleTypes.TupleData memory[] memory", "value": "tupleData"}, "id": 126, "name": "Identifier", "src": "742:9:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 127, "name": "Identifier", "src": "752:1:1"}], "id": 128, "name": "IndexAccess", "src": "742:12:1"}], "id": 129, "name": "MemberAccess", "src": "742:21:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "member_name": "someUint", "referencedDeclaration": 63, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 73, "type": "mapping(uint256 => struct tupleTypes.TupleData storage ref)", "value": "_tupleDataList"}, "id": 130, "name": "Identifier", "src": "766:14:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 69, "type": "uint256[] storage ref", "value": "_tupleDataIds"}, "id": 131, "name": "Identifier", "src": "781:13:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 132, "name": "Identifier", "src": "795:1:1"}], "id": 133, "name": "IndexAccess", "src": "781:16:1"}], "id": 134, "name": "IndexAccess", "src": "766:32:1"}], "id": 135, "name": "MemberAccess", "src": "766:41:1"}], "id": 136, "name": "Assignment", "src": "742:65:1"}], "id": 137, "name": "ExpressionStatement", "src": "742:65:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "someBool", "referencedDeclaration": 65, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData memory"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 97, "type": "struct tupleTypes.TupleData memory[] memory", "value": "tupleData"}, "id": 138, "name": "Identifier", "src": "815:9:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 139, "name": "Identifier", "src": "825:1:1"}], "id": 140, "name": "IndexAccess", "src": "815:12:1"}], "id": 141, "name": "MemberAccess", "src": "815:21:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "member_name": "someBool", "referencedDeclaration": 65, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 73, "type": "mapping(uint256 => struct tupleTypes.TupleData storage ref)", "value": "_tupleDataList"}, "id": 142, "name": "Identifier", "src": "839:14:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 69, "type": "uint256[] storage ref", "value": "_tupleDataIds"}, "id": 143, "name": "Identifier", "src": "854:13:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 105, "type": "uint256", "value": "i"}, "id": 144, "name": "Identifier", "src": "868:1:1"}], "id": 145, "name": "IndexAccess", "src": "854:16:1"}], "id": 146, "name": "IndexAccess", "src": "839:32:1"}], "id": 147, "name": "MemberAccess", "src": "839:41:1"}], "id": 148, "name": "Assignment", "src": "815:65:1"}], "id": 149, "name": "ExpressionStatement", "src": "815:65:1"}], "id": 150, "name": "Block", "src": "661:226:1"}], "id": 151, "name": "ForStatement", "src": "626:261:1"}, {"attributes": {"functionReturnParameters": 88}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 97, "type": "struct tupleTypes.TupleData memory[] memory", "value": "tupleData"}, "id": 152, "name": "Identifier", "src": "900:9:1"}], "id": 153, "name": "Return", "src": "893:16:1"}], "id": 154, "name": "Block", "src": "520:394:1"}], "id": 155, "name": "FunctionDefinition", "src": "424:490:1"}, {"attributes": {"documentation": null, "functionSelector": "4e9b5656", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getTupleIds", "overrides": null, "scope": 211, "stateMutability": "view", "virtual": false, "visibility": "external"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 156, "name": "ParameterList", "src": "938:2:1"}, {"children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "count", "overrides": null, "scope": 164, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 157, "name": "ElementaryTypeName", "src": "964:7:1"}], "id": 158, "name": "VariableDeclaration", "src": "964:13:1"}], "id": 159, "name": "ParameterList", "src": "963:15:1"}, {"children": [{"attributes": {"functionReturnParameters": 159}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "length", "referencedDeclaration": null, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 69, "type": "uint256[] storage ref", "value": "_tupleDataIds"}, "id": 160, "name": "Identifier", "src": "992:13:1"}], "id": 161, "name": "MemberAccess", "src": "992:20:1"}], "id": 162, "name": "Return", "src": "985:27:1"}], "id": 163, "name": "Block", "src": "979:38:1"}], "id": 164, "name": "FunctionDefinition", "src": "918:99:1"}, {"attributes": {"documentation": null, "functionSelector": "5fd378ff", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "setTupleTypes", "overrides": null, "scope": 211, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, "children": [{"children": [{"attributes": {"constant": false, "mutability": "mutable", "name": "tupleId", "overrides": null, "scope": 210, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 165, "name": "ElementaryTypeName", "src": "1049:7:1"}], "id": 166, "name": "VariableDeclaration", "src": "1049:15:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "someByte", "overrides": null, "scope": 210, "stateVariable": false, "storageLocation": "default", "type": "bytes32", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bytes32", "type": "bytes32"}, "id": 167, "name": "ElementaryTypeName", "src": "1070:7:1"}], "id": 168, "name": "VariableDeclaration", "src": "1070:16:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "someUint", "overrides": null, "scope": 210, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 169, "name": "ElementaryTypeName", "src": "1092:7:1"}], "id": 170, "name": "VariableDeclaration", "src": "1092:16:1"}, {"attributes": {"constant": false, "mutability": "mutable", "name": "someBool", "overrides": null, "scope": 210, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 171, "name": "ElementaryTypeName", "src": "1114:4:1"}], "id": 172, "name": "VariableDeclaration", "src": "1114:13:1"}], "id": 173, "name": "ParameterList", "src": "1043:88:1"}, {"attributes": {"parameters": [null]}, "children": [], "id": 174, "name": "ParameterList", "src": "1141:0:1"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "tryCall": false, "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "push", "referencedDeclaration": null, "type": "function (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 69, "type": "uint256[] storage ref", "value": "_tupleDataIds"}, "id": 175, "name": "Identifier", "src": "1147:13:1"}], "id": 177, "name": "MemberAccess", "src": "1147:18:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 166, "type": "uint256", "value": "tupleId"}, "id": 178, "name": "Identifier", "src": "1166:7:1"}], "id": 179, "name": "FunctionCall", "src": "1147:27:1"}], "id": 180, "name": "ExpressionStatement", "src": "1147:27:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bytes32"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "someByte", "referencedDeclaration": 61, "type": "bytes32"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 73, "type": "mapping(uint256 => struct tupleTypes.TupleData storage ref)", "value": "_tupleDataList"}, "id": 181, "name": "Identifier", "src": "1181:14:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 166, "type": "uint256", "value": "tupleId"}, "id": 182, "name": "Identifier", "src": "1196:7:1"}], "id": 183, "name": "IndexAccess", "src": "1181:23:1"}], "id": 184, "name": "MemberAccess", "src": "1181:32:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 168, "type": "bytes32", "value": "someByte"}, "id": 185, "name": "Identifier", "src": "1216:8:1"}], "id": 186, "name": "Assignment", "src": "1181:43:1"}], "id": 187, "name": "ExpressionStatement", "src": "1181:43:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "someUint", "referencedDeclaration": 63, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 73, "type": "mapping(uint256 => struct tupleTypes.TupleData storage ref)", "value": "_tupleDataList"}, "id": 188, "name": "Identifier", "src": "1230:14:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 166, "type": "uint256", "value": "tupleId"}, "id": 189, "name": "Identifier", "src": "1245:7:1"}], "id": 190, "name": "IndexAccess", "src": "1230:23:1"}], "id": 191, "name": "MemberAccess", "src": "1230:32:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 170, "type": "uint256", "value": "someUint"}, "id": 192, "name": "Identifier", "src": "1265:8:1"}], "id": 193, "name": "Assignment", "src": "1230:43:1"}], "id": 194, "name": "ExpressionStatement", "src": "1230:43:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "someBool", "referencedDeclaration": 65, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct tupleTypes.TupleData storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 73, "type": "mapping(uint256 => struct tupleTypes.TupleData storage ref)", "value": "_tupleDataList"}, "id": 195, "name": "Identifier", "src": "1279:14:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 166, "type": "uint256", "value": "tupleId"}, "id": 196, "name": "Identifier", "src": "1294:7:1"}], "id": 197, "name": "IndexAccess", "src": "1279:23:1"}], "id": 198, "name": "MemberAccess", "src": "1279:32:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 172, "type": "bool", "value": "someBool"}, "id": 199, "name": "Identifier", "src": "1314:8:1"}], "id": 200, "name": "Assignment", "src": "1279:43:1"}], "id": 201, "name": "ExpressionStatement", "src": "1279:43:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "tryCall": false, "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [null], "referencedDeclaration": 83, "type": "function (uint256,bytes32,uint256,bool)", "value": "TupleTypes"}, "id": 202, "name": "Identifier", "src": "1334:10:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 166, "type": "uint256", "value": "tupleId"}, "id": 203, "name": "Identifier", "src": "1345:7:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 168, "type": "bytes32", "value": "someByte"}, "id": 204, "name": "Identifier", "src": "1354:8:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 170, "type": "uint256", "value": "someUint"}, "id": 205, "name": "Identifier", "src": "1364:8:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 172, "type": "bool", "value": "someBool"}, "id": 206, "name": "Identifier", "src": "1374:8:1"}], "id": 207, "name": "FunctionCall", "src": "1334:49:1"}], "id": 208, "name": "EmitStatement", "src": "1329:54:1"}], "id": 209, "name": "Block", "src": "1141:247:1"}], "id": 210, "name": "FunctionDefinition", "src": "1021:367:1"}], "id": 211, "name": "ContractDefinition", "src": "98:1292:1"}], "id": 212, "name": "SourceUnit", "src": "39:1352:1"}, "compiler": {"name": "solc", "version": "0.6.12+commit.27d51765.Emscripten.clang"}, "networks": {"5157": {"events": {"0xc9bd4d6d4551cbbd492db819e98c425ec08809a4c1da651f4a70ac8cc1aad2a9": {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tupleDataId", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "someByte", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "someUint", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "someBool", "type": "bool"}], "name": "TupleTypes", "type": "event"}}, "links": {}, "address": "******************************************", "transactionHash": "0x287191c329d6aeb4546fb4c8991c77d62e13b2942e59f1a68ab3effee32aa140"}}, "schemaVersion": "3.3.3", "updatedAt": "2021-08-02T05:50:32.518Z", "networkType": "ethereum", "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}