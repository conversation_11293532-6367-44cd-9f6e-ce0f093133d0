package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util

import com.amazonaws.auth.AnonymousAWSCredentials
import com.amazonaws.services.s3.AmazonS3Client
import io.findify.s3mock.S3Mock
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.LocalProperties
import software.amazon.awssdk.services.s3.model.NoSuchBucketException
import software.amazon.awssdk.services.s3.model.NoSuchKeyException
import spock.lang.Specification

import java.util.stream.Collectors

class S3UtilSpec extends Specification {
    private static ApplicationProperties applicationProperties
    private static LocalProperties localProperties
    private static S3Mock s3mock
    private static S3Util s3Util

    private static final List<String> ALL_S3_KEYS = [
            "Currency.json",
            "Deposit.json",
            "Issuer.json",
            "Oracle.json",
            "Provider.json",
            "Token.json",
            "User.json",
            "Validator.json"
    ]
    private static final String NON_EXISTS_BUCKET = "non-exists-bucket"
    private static final String NON_EXISTS_OBJECT = "non-exists-object"

    def setupSpec() {
        applicationProperties = new ApplicationProperties()
        applicationProperties.setRegion("ap-northeast-1")
        applicationProperties.setContractFileBucketName("bucket")

        localProperties = new LocalProperties()
        localProperties.setUseLocalS3Bucket(true)
        localProperties.setLocalS3Uri("http://127.0.0.1:9001")
        localProperties.setS3AccessKey("access")
        localProperties.setS3SecretKey("secret")

        s3Util = new S3Util(applicationProperties, localProperties)

        s3mock = new S3Mock.Builder().withPort(9001).withInMemoryBackend().build()
        s3mock.start()

        ALL_S3_KEYS.forEach({ file ->
            AmazonS3Client client = new AmazonS3Client(new AnonymousAWSCredentials())
            client.setEndpoint("http://127.0.0.1:9001")
            client.createBucket(applicationProperties.getContractFileBucketName())
            client.putObject(applicationProperties.getContractFileBucketName(), file, "contents")
        })
    }

    def cleanupSpec() {
        s3mock.shutdown()
    }

    def "ListObjects: すべてのオベジェクトが取得できること"() {
        when:
        def response = s3Util.listObjects(applicationProperties.getContractFileBucketName())

        then:
        def keys = response.contents().stream().map({ obj -> obj.key() }).collect(Collectors.toList())
        keys.size() == ALL_S3_KEYS.size()
        keys.containsAll(ALL_S3_KEYS)
    }

    def "ListObjects: 存在しないバケットを指定した場合に例外が発生すること"() {
        when:
        s3Util.listObjects(NON_EXISTS_BUCKET)
        then:
        1 == 1
        thrown(NoSuchBucketException)
    }

    def "GetAllObjectsAsByteArrayList: 指定したオブジェクトが取得できること"() {
        when:
        def contents = s3Util.getAllObjectsAsByteArrayList(keys, applicationProperties.getContractFileBucketName())

        then:
        contents.size() == expected

        where:
        keys                      || expected
        ALL_S3_KEYS               || ALL_S3_KEYS.size()
        ALL_S3_KEYS.subList(0, 2) || 2
        []                        || 0
    }

    def "GetAllObjectsAsByteArrayList: 存在しないバケットを指定した場合に例外が発生すること"() {
        when:
        s3Util.getAllObjectsAsByteArrayList(ALL_S3_KEYS, NON_EXISTS_BUCKET)
        then:
        thrown(NoSuchBucketException)
    }

    def "GetAllObjectsAsByteArrayList: 存在しないオブジェクトを指定した場合に例外が発生すること"() {
        when:

        s3Util.getAllObjectsAsByteArrayList([NON_EXISTS_OBJECT], applicationProperties.getContractFileBucketName())
        then:
        thrown(NoSuchKeyException)
    }
}
