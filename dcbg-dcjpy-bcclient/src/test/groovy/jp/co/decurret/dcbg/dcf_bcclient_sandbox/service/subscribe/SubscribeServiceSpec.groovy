package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.subscribe

import io.reactivex.Flowable
import io.reactivex.disposables.Disposable
import io.reactivex.functions.Consumer
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.WebSocketUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.Web3j
import org.web3j.protocol.Web3jService
import org.web3j.protocol.websocket.WebSocketService
import org.web3j.protocol.websocket.events.NewHeadsNotification

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.mockStatic
import static org.mockito.Mockito.when

class SubscribeServiceSpec extends IntegrationTestBase {
    @Autowired
    private ApplicationProperties applicationProperties
    @Autowired
    private TransactionDatabase transactionDatabase

    private static MockedStatic<WebSocketUtil> webSocketUtilMockedStatic
    private static MockedStatic<Web3j> web3jMockedStatic

    def "setupSpec"() {
        WebSocketService webSocketService = Mockito.mock(WebSocketService.class)
        Flowable<NewHeadsNotification> notifications = Mockito.mock(Flowable.class)
        when(notifications.subscribe((Consumer) notNull(), (Consumer) notNull()))
                .thenReturn(new Disposable() {
                    @Override
                    void dispose() {
                    }

                    @Override
                    boolean isDisposed() {
                        return false
                    }
                })
        Web3j web3j = Mockito.mock(Web3j.class)
        when(web3j.newHeadsNotifications()).thenReturn(notifications)

        webSocketUtilMockedStatic = mockStatic(WebSocketUtil.class)
        webSocketUtilMockedStatic.when(WebSocketUtil.generateWebSocketService((String) notNull(), (String) notNull(), (boolean) notNull()))
                .thenReturn(webSocketService)
        web3jMockedStatic = mockStatic(Web3j.class)
        web3jMockedStatic.when(Web3j.build((Web3jService) notNull()))
                .thenReturn(web3j)

    }

    def "cleanupSpec"() {
        webSocketUtilMockedStatic.close()
        web3jMockedStatic.close()
    }

    def "Execute: WebSocket購読状態のDisposableが取得できること"() {
        when:
        SubscribeService subscribeService = new SubscribeService(transactionDatabase)
        Disposable disposable = subscribeService.execute(
                this.mainWebSocketConnectionPool,
                this.applicationProperties.getWebSocketUriHost(),
                this.applicationProperties.getWebSocketUriPort(),
                true
        )

        then:
        disposable.isDisposed() == false

        cleanup:
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose()
        }
    }
}
