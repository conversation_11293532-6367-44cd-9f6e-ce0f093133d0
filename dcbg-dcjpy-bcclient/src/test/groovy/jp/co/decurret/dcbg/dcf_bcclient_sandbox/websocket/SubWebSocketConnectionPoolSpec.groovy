package jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket

import io.reactivex.disposables.Disposable
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.WebSocketUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.mockStatic
import static org.mockito.Mockito.when

class SubWebSocketConnectionPoolSpec extends Specification {
    private static MockedStatic<WebSocketUtil> mocked

    def "setupSpec"() {
        WebSocketService service1 = Mockito.mock(WebSocketService.class)
        WebSocketService service2 = Mockito.mock(WebSocketService.class)

        mocked = mockStatic(WebSocketUtil.class)
        mocked.when(WebSocketUtil.generateWebSocketService((String) notNull(), (String) notNull(), (boolean) notNull()))
                .thenReturn(service1)
                .thenReturn(service2)
    }

    def "cleanupSpec"() {
        mocked.close()
    }

    def "CreateWebSocketConnection: WebSocketへの接続を2回実行した場合にコネクションが入れ替わること"() {
        when:
        MainWebSocketConnectionPool webSocketConnectionPool = new MainWebSocketConnectionPool()
        // 1回目
        webSocketConnectionPool.createWebSocketConnection("127.0.0.1", "1111", false)
        Web3j webSocketService1 = webSocketConnectionPool.getWebSocketConnection()
        // 2回目
        webSocketConnectionPool.createWebSocketConnection("127.0.0.1", "1111", false)
        Web3j webSocketService2 = webSocketConnectionPool.getWebSocketConnection()

        then:
        webSocketService1.equals(webSocketService2) == false
    }

    def "GetWebSocketConnection: 初期化していない状態で取得しようとした場合にIllegalStateExceptionが発生すること"() {
        when:
        MainWebSocketConnectionPool webSocketConnectionPool = new MainWebSocketConnectionPool()
        webSocketConnectionPool.getWebSocketConnection()

        then:
        thrown(IllegalStateException)
    }

    def "IsNewHeadsSubscriptionDisposed: nullの場合にtrueが返ること"() {
        when:
        MainWebSocketConnectionPool webSocketConnectionPool = new MainWebSocketConnectionPool()

        then:
        webSocketConnectionPool.isNewHeadsSubscriptionDisposed() == true
    }

    def "IsNewHeadsSubscriptionDisposed: disposedされている場合にtrueが返ること"() {
        when:
        Disposable disposable = Mockito.mock(Disposable.class)
        when(disposable.isDisposed()).thenReturn(true)

        MainWebSocketConnectionPool webSocketConnectionPool = new MainWebSocketConnectionPool()
        webSocketConnectionPool.setNewHeadsSubscription(disposable)

        then:
        webSocketConnectionPool.isNewHeadsSubscriptionDisposed() == true
    }

    def "IsNewHeadsSubscriptionDisposed: disposedされていない場合にfalseが返ること"() {
        when:
        Disposable disposable = Mockito.mock(Disposable.class)
        when(disposable.isDisposed()).thenReturn(false)

        MainWebSocketConnectionPool webSocketConnectionPool = new MainWebSocketConnectionPool()
        webSocketConnectionPool.setNewHeadsSubscription(disposable)

        then:
        webSocketConnectionPool.isNewHeadsSubscriptionDisposed() == false
    }
}
