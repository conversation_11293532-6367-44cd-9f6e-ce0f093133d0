package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.call

import com.fasterxml.jackson.databind.ObjectMapper
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.Application
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.APIRequestParameter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallService
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceOutput
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.when

@ActiveProfiles("test")
@WebMvcTest(CallController.class)
@ComponentScan(basePackages = "jp.co.decurret.dcbg.dcf_bcclient_sandbox")
class CallControllerSpec extends Specification {
    @MockBean
    private Application application
    @MockBean
    private ApplicationProperties applicationProperties
    @MockBean
    private CallService callService
    @MockBean
    private ContractInfo contractInfo
    @Autowired
    private MockMvc mockMvc;


    def "Call: リクエストが正常に処理されステータス200が返ってくること"() {
        when:
        when(callService.execute((CallServiceInput) notNull()))
                .thenReturn(new CallServiceOutput(new LinkedHashMap()))

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
    }

    def "Call: BadRequestExceptionが発生した時に400エラーが返ってくること"() {
        when:
        when(callService.execute((CallServiceInput) notNull()))
                .thenThrow(BadRequestException)

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
    }

    def "Call: BlockchainIOExceptionが発生した時に500エラーが返ってくること"() {
        when:
        when(callService.execute((CallServiceInput) notNull()))
                .thenThrow(BlockchainIOException)

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isInternalServerError())

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
    }
}
