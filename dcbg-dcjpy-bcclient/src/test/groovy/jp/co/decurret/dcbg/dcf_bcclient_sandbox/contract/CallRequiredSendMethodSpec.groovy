package jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract

import spock.lang.Specification

class CallRequiredSendMethodSpec extends Specification {
    def "ConvertArgs: マッピングに含まれる場合に正常に変換できること"() {
        when:
        CallRequiredSendMethod method = new CallRequiredSendMethod(
                "Token",
                "approve",
                new ArrayList<CallMethod>() {
                    {
                        add(new CallMethod("FinancialToken", "checkApprove"))
                    }
                },
                new LinkedHashMap())
        LinkedHashMap args = new LinkedHashMap() {
            {
                put("validatorId", 1)
                put("ownerId", 2)
                put("spenderId", 3)
                put("amount", 4)
                put("accountSignature", 5)
                put("info", 6)
                put("deadline", 7)
                put("signature", 8)
            }
        }
        LinkedHashMap result = method.remapArgs(args)

        then:
        result.size() == 8
        result.get("validatorId") == 1
        result.get("ownerId") == 2
        result.get("spenderId") == 3
        result.get("amount") == 4
        result.get("accountSignature") == 5
        result.get("info") == 6
        result.get("deadline") == 7
        result.get("signature") == 8
    }

    def "ConvertArgs: マッピングが空の場合に何も変換されないこと"() {
        when:
        CallRequiredSendMethod method = new CallRequiredSendMethod(
                "Token",
                "approve",
                new ArrayList<CallMethod>() {
                    {
                        add(new CallMethod("FinancialToken", "checkApprove"))
                    }
                },
                new LinkedHashMap())
        LinkedHashMap args = new LinkedHashMap() {
            {
                put("validatorId", 1)
                put("ownerId", 2)
                put("spenderId", 3)
                put("amount", 4)
                put("accountSignature", 5)
                put("info", 6)
                put("deadline", 7)
                put("signature", 8)
            }
        }
        LinkedHashMap result = method.remapArgs(args)

        then:
        result.size() == 8
        result.get("validatorId") == 1
        result.get("ownerId") == 2
        result.get("spenderId") == 3
        result.get("amount") == 4
        result.get("accountSignature") == 5
        result.get("info") == 6
        result.get("deadline") == 7
        result.get("signature") == 8
    }
}
