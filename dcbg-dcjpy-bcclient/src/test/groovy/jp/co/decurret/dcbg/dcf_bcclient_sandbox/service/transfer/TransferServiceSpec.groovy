package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.RequestTransactionMap
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.mock.mockito.SpyBean
import software.amazon.awssdk.services.sqs.model.SendMessageResponse

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.*

class TransferServiceSpec extends IntegrationTestBase {
    @SpyBean
    private TransactionDatabase transactionDatabase
    @SpyBean
    private EthRequestExecutor ethRequestExecutor
    @MockBean
    private RequestTransactionMap requestTransactionMap
    @SpyBean
    private TransferService transferService

    private static final String MESSAGE_ID = "message-id"
    private static final String TRANSACTION_HASH = "transaction-hash"

    def "setup"() {
        // doCall実行時に固定の結果値を返す
        doReturn(new LinkedHashMap<String, Object>() {
            {
                put("err", "")
                put("success", true)
            }
        }).when(this.transferService).transferCall((TransferServiceInput) notNull())

        // enqueue時にResponseを返す
        SendMessageResponse sendMessageResponse = Mockito.mock(SendMessageResponse.class)
        when(sendMessageResponse.messageId()).thenReturn(MESSAGE_ID)
        doReturn(sendMessageResponse)
                .when(this.transferService).enqueueRequest((TransferServiceInput) notNull())
        when(this.requestTransactionMap.getTransactionHash((String) notNull())).thenReturn(TRANSACTION_HASH)

        // transactionDatabaseのisCompletedは常にtrueを返す
        Mockito.doAnswer(new Answer<Boolean>() {
            @Override
            Boolean answer(InvocationOnMock invocation) throws Throwable {
                transactionDatabase.begin(TRANSACTION_HASH)
                transactionDatabase.toQueued(TRANSACTION_HASH)
                transactionDatabase.toExecuted(TRANSACTION_HASH)
                transactionDatabase.toSuccess(TRANSACTION_HASH)
                return (boolean) invocation.callRealMethod()
            }
        }).when(transactionDatabase).isCompleted(TRANSACTION_HASH)
    }


    def "Execute: Transferリクエストが実行できること"() {
        when:
        doNothing()
                .when(this.transferService).checkTransferParams((TransferServiceInput) notNull())
        TransferServiceOutput transferServiceOutput = this.transferService.execute(new TransferServiceInput(contractName, method, args))

        then:
        transferServiceOutput.isResult() == true
        transferServiceOutput.getError() == ""
        transferServiceOutput.getTransactionHash() == TRANSACTION_HASH

        where:
        contractName | method     | args
        "Token"      | "transfer" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 111)
                put("fromAccountId", 1)
                put("sendAccountId", 1)
                put("toAccountId", 2)
                put("amount", 100)
                put("miscValue1", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("miscValue2", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("deadline", **********)
                put("signature", "0x3100000000000000000000000000000000000000000000000000000000000000")
            }
        }
    }

    def "CheckTransferParams: パラメータの確認がOKになること"() {
        when:
        this.transferService.checkTransferParams(new TransferServiceInput(contractName, method, args))

        then:
        notThrown(BadRequestException.class)

        where:
        contractName | method     | args
        "Token"      | "transfer" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 111)
                put("fromAccountId", 1)
                put("sendAccountId", 1)
                put("toAccountId", 2)
                put("amount", 100)
                put("miscValue1", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("miscValue2", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("deadline", **********)
                put("signature", "0x3100000000000000000000000000000000000000000000000000000000000000")
            }
        }
    }

    def "CheckTransferParams: パラメータの確認がNGになること"() {
        when:
        this.transferService.checkTransferParams(new TransferServiceInput(contractName, method, args))

        then:
        thrown(BadRequestException.class)

        where:
        contractName | method     | args
        "Token"      | "transfer" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 111)
                put("fromAccountId", 1)
                put("sendAccountId", 1)
                put("toAccountId", 2)
                put("amount", 100)
                put("miscValue1", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("miscValue2", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("deadline", **********)
                // put("signature", "0x3100000000000000000000000000000000000000000000000000000000000000")
            }
        }
    }

    def "Execute: コントラクトの結果からsuccessが取得できない場合、BlockchainIOExceptionが返ること"() {
        setup:
        doReturn(new LinkedHashMap<String, Object>()).when(this.transferService).transferCall((TransferServiceInput) notNull())

        when:
        doNothing()
                .when(this.transferService).checkTransferParams((TransferServiceInput) notNull())
        TransferServiceOutput transferServiceOutput = this.transferService.execute(new TransferServiceInput(contractName, method, args))

        then:
        thrown(BlockchainIOException)

        where:
        contractName | method     | args
        "Token"      | "transfer" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 111)
                put("fromAccountId", 1)
                put("sendAccountId", 1)
                put("toAccountId", 2)
                put("amount", 100)
                put("miscValue1", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("miscValue2", "0x3100000000000000000000000000000000000000000000000000000000000000")
                put("deadline", **********)
                put("signature", "0x3100000000000000000000000000000000000000000000000000000000000000")
            }
        }
    }
}
