package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.*
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ConvertedInputData
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.WebSocketConnectionPoolBase
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.SpyBean
import org.web3j.protocol.core.methods.response.EthCall

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.*

class CallServiceSpec extends IntegrationTestBase {
    @Autowired
    private CallService callService
    @SpyBean
    private ServiceInputConverter serviceInputConverter
    @SpyBean
    private EthRequestExecutor ethRequestExecutor
    @SpyBean
    private ContractInfo contractInfo

    def "setup"() {
        // doCall実行時に固定の結果値を返す
        EthCall ethCall = Mockito.mock(EthCall.class)
        when(ethCall.getValue()).thenReturn("0xcc816c88000000000000000000000000000000000000000000000000000000000000007b0000000000000000000000000000000000000000000000000000000000000001")
        doReturn(ethCall)
                .when(ethRequestExecutor).doCall((String) notNull(), (String) notNull(), (WebSocketConnectionPoolBase) notNull())
    }

    def "Execute: CallServiceが正常に実行できること"() {
        when:
        doCallRealMethod()
                .when(this.contractInfo).getContractABI((String) notNull())
        doCallRealMethod()
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        CallServiceOutput callServiceOutput = this.callService.execute(new CallServiceInput(contractName, method, args))

        then:
        callServiceOutput.getHashMapData() != null

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }
    }

    def "Execute: コントラクト名が存在しない場合にBadRequestExceptionが発生すること"() {
        when:
        doCallRealMethod()
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        doReturn(new ContractABI("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.serviceInputConverter).extractExternalContractABI((ServiceInputBase) notNull())
        this.callService.execute(new CallServiceInput(contractName, method, args))

        then:
        thrown(ex)

        where:
        contractName | method           | args | ex
        "none"       | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }                                      | BadRequestException
    }

    def "Execute: メソッド名が存在しない場合にBadRequestExceptionが発生すること"() {
        when:
        doCallRealMethod()
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        doReturn(new ContractABI("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.serviceInputConverter).extractExternalContractABI((ServiceInputBase) notNull())
        this.callService.execute(new CallServiceInput(contractName, method, args))

        then:
        thrown(ex)

        where:
        contractName | method | args | ex
        "Validator"  | "none" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }                            | BadRequestException
    }

    def "Execute: パラメータが不足している場合にContractNotFoundExceptionが発生すること"() {
        when:
        this.callService.execute(new CallServiceInput(contractName, method, args))

        then:
        thrown(ex)

        where:
        contractName | method           | args | ex
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
            }
        }                                      | BadRequestException
    }

    def "Execute: tuple型が結果に含まれる場合にBadRequestExceptionが発生すること"() {
        when:
        EthCall ethCall = Mockito.mock(EthCall.class)
        when(ethCall.getValue()).thenReturn("0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007d00000000000000000000000000000000000000000000000000000000000000001")
        doReturn(ethCall)
                .when(ethRequestExecutor).doCall((String) notNull(), (String) notNull(), (WebSocketConnectionPoolBase) notNull())
        CallServiceOutput output = this.callService.execute(new CallServiceInput(contractName, method, args))

        then:
        thrown(BadRequestException)

        where:
        contractName | method          | args
        "TupleTypes" | "getTupleTypes" | new LinkedHashMap<String, Object>()
    }

    def "Execute: 特定メソッドの場合に共有DTLにCallされること"() {
        when:
        doReturn(new ContractABI("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.contractInfo).getContractABI((String) notNull())
        doReturn(new ContractFunction("name", new ArrayList<ContractInputParameter>(), new ArrayList<ContractOutputParameter>()))
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        doReturn(new ConvertedInputData("", ""))
                .when(this.serviceInputConverter).convert((ServiceInputBase) notNull(), (ContractFunction) notNull(), (String) notNull())

        EthCall ethCall = Mockito.mock(EthCall.class)
        when(ethCall.getValue()).thenReturn("0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007d00000000000000000000000000000000000000000000000000000000000000001")
        doReturn(ethCall)
                .when(ethRequestExecutor).doCall((String) notNull(), (String) notNull(), (SubWebSocketConnectionPool) notNull())
        doReturn(new LinkedHashMap() {
            {
                put("err", "")
                put("success", true)
            }
        }).when(ethRequestExecutor).decodeCallResult((String) notNull(), (ContractFunction) notNull())

        this.callService.execute(new CallServiceInput(contractName, method, args))

        then:
        notThrown(Exception)

        where:
        contractName     | method       | args
        "FinancialToken" | "getAccount" | new LinkedHashMap<String, Object>()
        "FinancialToken" | "getAccount" | new LinkedHashMap<String, Object>()
    }
}
