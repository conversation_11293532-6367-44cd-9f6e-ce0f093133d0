package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction

import org.mockito.Mockito
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthGetTransactionCount
import spock.lang.Specification

import java.util.concurrent.ConcurrentLinkedQueue

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.when

class TransactionManagerSpec extends Specification {
    private static final int TASK_NUM = 1000

    def "GetNonce: 重複なしのNonceがスレッドセーフで取得できること"() {
        when:
        EthGetTransactionCount ethGetTransactionCount = Mockito.mock(EthGetTransactionCount.class)
        when(ethGetTransactionCount.getTransactionCount()).thenReturn(new BigInteger("1"))
        Request<?, EthGetTransactionCount> request = Mockito.mock(Request.class)
        when(request.send()).thenReturn(ethGetTransactionCount)
        Web3j web3j = Mockito.mock(Web3j.class)
        when(web3j.ethGetTransactionCount((String)notNull(), (DefaultBlockParameter) notNull())).thenReturn(request)

        NonceManager nonceManager = new NonceManager()

        Queue<BigInteger> queue = new ConcurrentLinkedQueue<BigInteger>()
        //スレッド生成、実行
        def th = new Thread[TASK_NUM]
        for (int i = 0; i < TASK_NUM; i++) {
            th[i] = new Thread({ ->
                queue.add(nonceManager.getNonce(web3j, "address"))
            })
            th[i].start()
        }
        //スレッド終了まで待機
        for (int i = 0; i < TASK_NUM; i++) {
            th[i].join()
        }

        then:
        queue.size() == TASK_NUM
        queue.stream().distinct().collect().size() == TASK_NUM
        queue.min() == 1
        queue.max() == TASK_NUM
    }
}
