package jp.co.decurret.dcbg.dcf_bcclient_sandbox

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractAddressABIPair
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ABIParseException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.AbiFormat
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.subscribe.SubscribeService
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.ContractABIConvertUtil
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ActiveProfiles
import spock.lang.Specification

@ActiveProfiles("test")
@SpringBootTest(
        classes = [ContractInfoConfig.class]
)
abstract class IntegrationTestBase extends Specification {
    @MockBean
    private SubscribeService subscribeService
    @MockBean
    protected MainWebSocketConnectionPool mainWebSocketConnectionPool
    @MockBean
    protected SubWebSocketConnectionPool subWebSocketConnectionPool
    @Autowired
    protected ContractInfo contractInfo

    @TestConfiguration
    static class ContractInfoConfig {
        @Bean
        ContractInfo contractInfo() {
            ContractInfo contractInfo = new ContractInfo()

            ClassLoader classLoader = getClass().getClassLoader()
            File providerJson = new File(classLoader.getResource("Provider.json").getFile())
            File validatorJson = new File(classLoader.getResource("Validator.json").getFile())
            File tupleTypesJson = new File(classLoader.getResource("TupleTypes.json").getFile())
            File tokenJson = new File(classLoader.getResource("Token.json").getFile())
            File testJson = new File(classLoader.getResource("Test.json").getFile())

            Map<String, byte[]> abiContents = Map.of(
                    "Validator.json", validatorJson.getBytes(),
                    "TupleTypes.json", tupleTypesJson.getBytes(),
                    "Token.json", tokenJson.getBytes(),
                    "Test.json", testJson.getBytes()
            );

            abiContents.forEach({ String key, byte[] content ->
                try {
                    ContractABI abi = ContractABIConvertUtil.convertByteToObject(AbiFormat.TRUFFLE, key, content);
                    contractInfo.map.put(abi.getName(), new ContractAddressABIPair(abi.getAddress(), abi));
                } catch (IOException e) {
                    throw new ABIParseException("ABI file parsing failed", e);
                }
            })

            // Provider.json は HartHat の形式を想定
            ContractABI abiProvider = ContractABIConvertUtil.convertByteToObject(AbiFormat.HARDHAT, "Provider.json", providerJson.getBytes());
            contractInfo.map.put(abiProvider.getName(), new ContractAddressABIPair(abiProvider.getAddress(), abiProvider));

            return contractInfo
        }
    }
}
