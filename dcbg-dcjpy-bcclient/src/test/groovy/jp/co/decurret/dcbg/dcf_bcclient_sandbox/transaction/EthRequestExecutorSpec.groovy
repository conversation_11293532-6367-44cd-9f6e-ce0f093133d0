package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendServiceOutput
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.SpyBean
import org.web3j.protocol.core.methods.response.EthSendTransaction

import static org.mockito.Mockito.when

class EthRequestExecutorSpec extends IntegrationTestBase {
    @SpyBean
    private TransactionDatabase transactionDatabase
    @Autowired
    private EthRequestExecutor ethRequestExecutor
    @Autowired
    private ServiceInputConverter serviceInputConverter

    private static final String TRANSACTION_HASH = "0x11"

    def "setup"() {
        // transactionDatabaseのisCompletedは常にtrueを返す
        Mockito.doAnswer(new Answer<Boolean>() {
            @Override
            Boolean answer(InvocationOnMock invocation) throws Throwable {
                transactionDatabase.toExecuted(TRANSACTION_HASH)
                transactionDatabase.toSuccess(TRANSACTION_HASH)
                return (boolean) invocation.callRealMethod()
            }
        }).when(transactionDatabase).isCompleted(TRANSACTION_HASH)
    }

    def "FetchTransactionResult: 結果を取得できること"() {
        when:
        EthSendTransaction ethSendTransaction = Mockito.mock(EthSendTransaction.class)
        when(ethSendTransaction.getError())
                .thenReturn(null)
        when(ethSendTransaction.getTransactionHash())
                .thenReturn(TRANSACTION_HASH)
        SendServiceOutput output = this.ethRequestExecutor.fetchTransactionResult(ethSendTransaction)

        then:
        output.getTransactionHash() == TRANSACTION_HASH
        output.getError() == ""
    }

    def "DecodeCallResult: tuple型を含むCall結果をデコードするとUnsupportedOperationExceptionが発生すること"() {
        when:
        CallServiceInput callServiceInput = new CallServiceInput(contractName, method, args)
        ContractABI contractABI = this.contractInfo.getContractABI(callServiceInput.getContractName())
        ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI)
        LinkedHashMap result = this.ethRequestExecutor.decodeCallResult(
                "0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007d00000000000000000000000000000000000000000000000000000000000000001",
                contractFunction
        )

        then:
        thrown(UnsupportedOperationException)

        where:
        contractName | method          | args
        "TupleTypes" | "getTupleTypes" | new LinkedHashMap<String, Object>()
    }

    def "DecodeCallResult: 配列のuint256型、bytes32型、bool型がデコードできること"() {
        when:
        CallServiceInput callServiceInput = new CallServiceInput(contractName, method, args)
        ContractABI contractABI = this.contractInfo.getContractABI(callServiceInput.getContractName())
        ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI)
        LinkedHashMap result = this.ethRequestExecutor.decodeCallResult(
                "0x0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000033078313030000000000000000000000000000000000000000000000000000000307831303100000000000000000000000000000000000000000000000000000030783130320000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001",
                contractFunction
        )
        List<String> someUint = result.get("someUint")
        List<String> someByte = result.get("someByte")
        List<Boolean> someBool = result.get("someBool")
        String count = result.get("count")

        then:
        count == "3"
        someUint.size() == 3
        someByte.size() == 3
        someBool.size() == 3

        someUint.get(0) == "1"
        someUint.get(1) == "2"
        someUint.get(2) == "3"

        someByte.get(0) == "0x3078313030000000000000000000000000000000000000000000000000000000"
        someByte.get(1) == "0x3078313031000000000000000000000000000000000000000000000000000000"
        someByte.get(2) == "0x3078313032000000000000000000000000000000000000000000000000000000"

        someBool.get(0) == true
        someBool.get(1) == false
        someBool.get(2) == true

        where:
        contractName | method     | args
        "Test"       | "getArray" | new LinkedHashMap<String, Object>()
    }
}
