package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.send

import com.fasterxml.jackson.databind.ObjectMapper
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.Application
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.APIRequestParameter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.NoRevertReasonException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.TransferProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendService
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendServiceInput
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendServiceOutput
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer.TransferService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.when

@ActiveProfiles("test")
@WebMvcTest(SendController.class)
@ComponentScan(basePackages = "jp.co.decurret.dcbg.dcf_bcclient_sandbox")
class SendControllerSpec extends Specification {
    @MockBean
    private Application application
    @MockBean
    private ApplicationProperties applicationProperties
    @Autowired
    private TransferProperties transferProperties
    @MockBean
    private SendService sendService
    @MockBean
    private TransferService transferService
    @MockBean
    private ContractInfo contractInfo
    @Autowired
    private MockMvc mockMvc


    def "Send: リクエストが正常に処理されステータス200が返ってくること"() {
        when:
        when(sendService.execute((SendServiceInput) notNull()))
                .thenReturn(new SendServiceOutput(true, "", "0x00"))

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: Transferリクエストが通常のSendリクエストで実行されること"() {
        when:
        when(sendService.execute((SendServiceInput) notNull()))
                .thenReturn(new SendServiceOutput(true, "", "0x00"))

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        contractName | method     | args
        "Token"      | "transferSingle" | new LinkedHashMap<>()
    }

    def "Send: BadRequestExceptionが発生した時に400エラーが返ってくること"() {
        when:
        when(sendService.execute((SendServiceInput) notNull()))
                .thenThrow(BadRequestException)

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: BlockchainIOExceptionが発生した時に500エラーが返ってくること"() {
        when:
        when(sendService.execute((SendServiceInput) notNull()))
                .thenThrow(BlockchainIOException)

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isInternalServerError())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: NoRevertReasonExceptionが発生した時に500エラーが返ってくること"() {
        when:
        when(sendService.execute((SendServiceInput) notNull()))
                .thenThrow(NoRevertReasonException)

        APIRequestParameter param = new APIRequestParameter(contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isInternalServerError())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }
}
