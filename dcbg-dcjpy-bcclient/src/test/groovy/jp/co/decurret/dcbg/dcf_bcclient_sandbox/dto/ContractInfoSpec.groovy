package jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput

class ContractInfoSpec extends IntegrationTestBase {
    def "Convert: コントラクト名が存在しない場合にContractNotFoundExceptionが発生すること"() {
        when:
        CallServiceInput callServiceInput = new CallServiceInput(contractName, method, args)
        this.contractInfo.getContractABI(callServiceInput.getContractName())

        then:
        thrown(ex)

        where:
        contractName | method           | args | ex
        "none"       | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                                      | ContractNotFoundException
    }

    def "FilterJsonEntries: .jsonで終わるキー名以外を除外できること"() {
        when:
        Map<String, byte[]> abiContents = new HashMap<>();
        abiContents.put("aaa.json", new byte[0]) // これが残る
        abiContents.put("bbb.jsonp", new byte[0])
        abiContents.put("json", new byte[0])
        abiContents.put(".json.test", new byte[0])
        abiContents.put("test-json", new byte[0])
        abiContents.put("ajson", new byte[0])
        abiContents.put("a json", new byte[0])
        abiContents.put(".json.", new byte[0])
        abiContents.put(".JSON", new byte[0])
        Map<String, byte[]> filtered = this.contractInfo.filterJsonEntries(abiContents)

        then:
        filtered.size() == 1
        filtered.containsKey("aaa.json")
    }
}
