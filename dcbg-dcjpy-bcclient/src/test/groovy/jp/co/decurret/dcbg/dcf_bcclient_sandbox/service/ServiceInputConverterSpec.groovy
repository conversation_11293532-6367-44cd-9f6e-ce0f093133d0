package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput
import org.springframework.beans.factory.annotation.Autowired

class ServiceInputConverterSpec extends IntegrationTestBase {
    @Autowired
    private ServiceInputConverter serviceInputConverter

    def "Convert: 変換が正常に実行されること"() {
        when:
        CallServiceInput callServiceInput = new CallServiceInput(contractName, method, args)
        ContractABI contractABI = this.contractInfo.getContractABI(callServiceInput.getContractName())
        ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI)
        ConvertedInputData convertedInputData = this.serviceInputConverter.convert(callServiceInput, contractFunction, contractABI.getAddress())

        then:
        convertedInputData.getContractAddress() == contractAddress
        convertedInputData.getEncodedFunction() == encodedFunction

        where:
        contractName | method           | args || contractAddress                              | encodedFunction
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                                      || "******************************************" | "0xcc816c88000000000000000000000000000000000000000000000000000000000000007b0000000000000000000000000000000000000000000000000000000000000001"
        "Provider"   | "hasProvUserID"  | new LinkedHashMap<String, Object>() {
            {
                put("provID", 123)
                put("userID", 321)
            }
        }                                      || "******************************************" | "0x0a17cdbe000000000000000000000000000000000000000000000000000000000000007b0000000000000000000000000000000000000000000000000000000000000141"
    }

    def "Convert: メソッド名が存在しない場合にContractNotFoundExceptionが発生すること"() {
        when:
        CallServiceInput callServiceInput = new CallServiceInput(contractName, method, args)
        ContractABI contractABI = this.contractInfo.getContractABI(callServiceInput.getContractName())
        ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI)
        this.serviceInputConverter.convert(callServiceInput, contractFunction, contractABI.getAddress())

        then:
        thrown(ex)

        where:
        contractName | method | args | ex
        "Validator"  | "none" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                            | ContractNotFoundException
    }

    def "Convert: 引数不足の場合にBadRequestExceptionが発生すること"() {
        when:
        CallServiceInput callServiceInput = new CallServiceInput(contractName, method, args)
        ContractABI contractABI = this.contractInfo.getContractABI(callServiceInput.getContractName())
        ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI)
        this.serviceInputConverter.convert(callServiceInput, contractFunction, contractABI.getAddress())

        then:
        thrown(ex)

        where:
        contractName | method           | args | ex
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
            }
        }                                      | BadRequestException
    }
}
