package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction


import spock.lang.Specification

class RequestTransactionMapSpec extends Specification {
    private static final String REQUEST_ID = "requestId"
    private static final String TRANSACTION_HASH = "transactionHash"

    def "PutValue: 正常に登録できること"() {
        when:
        RequestTransactionMap requestTransactionMap = new RequestTransactionMap()
        requestTransactionMap.putValue(REQUEST_ID, TRANSACTION_HASH)

        then:
        String transactionHash = requestTransactionMap.getTransactionHash(REQUEST_ID)
        transactionHash == TRANSACTION_HASH
    }

    def "RemoveKey: 正常に削除できること"() {
        when:
        RequestTransactionMap requestTransactionMap = new RequestTransactionMap()
        requestTransactionMap.putValue(REQUEST_ID, TRANSACTION_HASH)

        then:
        requestTransactionMap.removeKey(REQUEST_ID)
        requestTransactionMap.getTransactionHash(REQUEST_ID) == null
    }

    def "HasTransactionHash: 正常に判定できること"() {
        when:
        RequestTransactionMap requestTransactionMap = new RequestTransactionMap()
        requestTransactionMap.putValue(REQUEST_ID, TRANSACTION_HASH)

        then:
        requestTransactionMap.hasTransactionHash(TRANSACTION_HASH) == true
        requestTransactionMap.hasTransactionHash("none") == false
    }
}
