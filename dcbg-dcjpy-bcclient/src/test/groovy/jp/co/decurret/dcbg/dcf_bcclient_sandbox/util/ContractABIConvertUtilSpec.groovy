package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractOutputParameter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractParameterBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.AbiFormat
import org.web3j.abi.datatypes.DynamicArray
import org.web3j.abi.datatypes.Type
import spock.lang.Specification

class ContractABIConvertUtilSpec extends Specification {
    private byte[] providerJsonContent
    private byte[] validatorJsonContent
    private byte[] tupleTypesJsonContent
    private byte[] testJsonContent

    def setupSpec() {
        ClassLoader classLoader = getClass().getClassLoader()
        File providerJson = new File(classLoader.getResource("Provider.json").getFile())
        this.providerJsonContent = providerJson.getBytes()
        File validatorJson = new File(classLoader.getResource("Validator.json").getFile())
        this.validatorJsonContent = validatorJson.getBytes()
        File tupleTypesJson = new File(classLoader.getResource("TupleTypes.json").getFile())
        this.tupleTypesJsonContent = tupleTypesJson.getBytes()
        File testJson = new File(classLoader.getResource("Test.json").getFile())
        this.testJsonContent = testJson.getBytes()
    }

    def "ConvertByteToObject: JSONからオブジェクトが生成できること"() {
        when:
        ContractABI abi = ContractABIConvertUtil.convertByteToObject(abiFormat, contractName + ".json", content)

        then:
        abi.getName() == contractName
        abi.getAddress() == address
        abi.getFunctionInfos().size() == functionSize
        ContractFunction function = abi.getFunctionInfo(functionName)
        function.getName() == functionName
        function.getInputParameterList().size() == inputSize
        function.getOutputParameterList().size() == outputSize

        where:
        content                    || contractName | abiFormat         | address                                      | functionSize | functionName      | inputSize | outputSize
        this.providerJsonContent   || "Provider"   | AbiFormat.HARDHAT | "0xf17e6cA26072E0668d1a24F5Be91f45118B6E30c" | 20           | "addProviderRole" | 4         | 0
        this.validatorJsonContent  || "Validator"  | AbiFormat.TRUFFLE | "0x3C25BF12D614545d7535abf7Ca1014627140B7D1" | 13           | "hasValidatorID"  | 2         | 1
        this.tupleTypesJsonContent || "tupleTypes" | AbiFormat.TRUFFLE | "0x90774f99783f1238e1193cD7030FB3d686D0786E" | 4            | "getTupleTypes"   | 0         | 1
    }

    def "ConvertByteToObject: Tupleが正常に変換できること"() {
        when:
        ContractABI abi = ContractABIConvertUtil.convertByteToObject(AbiFormat.TRUFFLE, contractName + ".json", content)

        then:
        abi.getName() == contractName
        abi.getAddress() == address
        abi.getFunctionInfos().size() == functionSize
        ContractFunction function = abi.getFunctionInfo(functionName)
        function.getName() == functionName
        function.getInputParameterList().size() == inputSize
        function.getOutputParameterList().size() == outputSize

        ContractOutputParameter outputParameter = function.getOutputParameterList().get(0)
        outputParameter.getName() == "tupleDataList"
        outputParameter.getTypeString() == "tuple[]"
        outputParameter.getClazz() == DynamicArray.class

        where:
        content                    || contractName | address                                      | functionSize | functionName    | inputSize | outputSize | componentSize
        this.tupleTypesJsonContent || "tupleTypes" | "0x90774f99783f1238e1193cD7030FB3d686D0786E" | 4            | "getTupleTypes" | 0         | 1          | 3
    }


    def "EncodeFunction: 正常にエンコードできること"() {
        when:
        ContractABI abi = ContractABIConvertUtil.convertByteToObject(AbiFormat.HARDHAT, "dummy.json", content)
        ContractFunction contractFunctionInfo = abi.getFunctionInfo(functionName)
        List<Object> inputParams = new ArrayList<>()
        for (ContractParameterBase param : contractFunctionInfo.getInputParameterList()) {
            String key = param.getName()
            inputParams.add(EthDataTypeUtil.toEthParamObject(param.getTypeString(), args.get(key)))
        }
        String encoded = ContractABIConvertUtil.encodeFunction(contractFunctionInfo, inputParams)

        then:
        encoded == expected

        where:
        content                  || functionName      | args | expected
        this.providerJsonContent || "addProviderRole" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 123)
                put("eoaProv", "0x00000400")
                put("deadline", **********)
                put("signature", (byte[]) [49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
            }
        }                                                    | "0xe15a3d34000000000000000000000000000000000000000000000000000000000000007b00000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000060c317e3000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000203100000000000000000000000000000000000000000000000000000000000000"
    }

    def "EncodeFunction: パラメータ数が不足している場合にIllegalStateException例外が発生すること"() {
        when:
        ContractABI abi = ContractABIConvertUtil.convertByteToObject(AbiFormat.HARDHAT, "dummy.json", content)
        ContractFunction contractFunctionInfo = abi.getFunctionInfo(functionName)
        List<Object> inputParams = new ArrayList<>()
        for (ContractParameterBase param : contractFunctionInfo.getInputParameterList()) {
            String key = param.getName()
            if (args.get(key) != null) {
                inputParams.add(EthDataTypeUtil.toEthParamObject(param.getTypeString(), args.get(key)))
            }
        }
        ContractABIConvertUtil.encodeFunction(contractFunctionInfo, inputParams)

        then:
        thrown(IllegalStateException)

        where:
        content                  || functionName      | args
        this.providerJsonContent || "addProviderRole" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 123)
                put("eoaProv", "0x00000400")
                put("deadline", **********)
            }
        }
    }

    def "DecodeFunction: 正常にデコードできること"() {
        when:
        ContractABI abi = ContractABIConvertUtil.convertByteToObject(AbiFormat.TRUFFLE, "dummy.json", content)
        ContractFunction contractFunctionInfo = abi.getFunctionInfo(functionName)
        List<Object> inputParams = new ArrayList<>()
        for (ContractParameterBase param : contractFunctionInfo.getInputParameterList()) {
            String key = param.getName()
            inputParams.add(EthDataTypeUtil.toEthParamObject(param.getTypeString(), args.get(key)))
        }
        String encoded = ContractABIConvertUtil.encodeFunction(contractFunctionInfo, inputParams)
        List<Type> types = ContractABIConvertUtil.decodeFunction(contractFunctionInfo, encoded)

        then:
        types.size() == 1
        types.get(0).getTypeAsString() == typeString
        types.get(0).getValue() == value

        where:
        content                   || functionName     | args | outputSize | typeString | value
        this.validatorJsonContent || "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                                                    | 1          | "string"   | ""
    }

    def "DecodeFunction: tuple型をデコードする際にUnsupportedOperationExceptionが発生すること"() {
        when:
        ContractABI abi = ContractABIConvertUtil.convertByteToObject(AbiFormat.TRUFFLE, "dummy.json", content)
        ContractFunction function = abi.getFunctionInfo(functionName)
        String value = "0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007d00000000000000000000000000000000000000000000000000000000000000001"
        List<Type> types = ContractABIConvertUtil.decodeFunction(function, value)

        then:
        thrown(UnsupportedOperationException)

        where:
        content                    | functionName    || val1                                                                 | val2   | val3 | type1     | type2     | type3
        this.tupleTypesJsonContent | "getTupleTypes" || "0x1000000000000000000000000000000000000000000000000000000000000000" | "2000" | true | "bytes32" | "uint256" | "bool"
    }
}
