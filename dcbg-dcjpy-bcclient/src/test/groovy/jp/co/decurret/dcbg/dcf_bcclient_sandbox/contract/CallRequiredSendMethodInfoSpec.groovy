package jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract

import spock.lang.Specification

class CallRequiredSendMethodInfoSpec extends Specification {
    def "Contains: 正常に判定ができること"() {
        when:
        boolean result = CallRequiredSendMethodInfo.contains(contractName, contractMethod)

        then:
        result == bool

        where:
        contractName             | contractMethod        || bool
        "Validator"              | "addAccount"          || true
        "Validator"              | "syncAccount"         || true
        "Token"                  | "approve"             || true
        "JPYTokenTransferBridge" | "transfer"            || true
        "Validator"              | "setTerminated"       || true
        "Token"                  | "mint"                || true
        "Token"                  | "burn"                || true
        "Token"                  | "transferSingle"      || true
        "Validator"              | "setAccountOpenState" || true
        "None"                   | "transfer"            || false
        "Token"                  | "None"                || false
    }

    def "GetContractMethod: 正常にメソッドが取得できること"() {
        when:
        CallRequiredSendMethod method = CallRequiredSendMethodInfo.getContractMethod(contractName, contractMethod)

        then:
        Objects.nonNull(method) == bool

        where:
        contractName             | contractMethod        || bool
        "Validator"              | "addAccount"          || true
        "Validator"              | "syncAccount"         || true
        "Token"                  | "approve"             || true
        "JPYTokenTransferBridge" | "transfer"            || true
        "Validator"              | "setTerminated"       || true
        "Token"                  | "mint"                || true
        "Token"                  | "burn"                || true
        "Token"                  | "transferSingle"      || true
        "Validator"              | "setAccountOpenState" || true
        "None"                   | "transfer"            || false
        "Token"                  | "None"                || false
    }

    def "GetContractMethod: Callメソッドのマッピングが取得できること"() {
        when:
        CallRequiredSendMethod method = CallRequiredSendMethodInfo.getContractMethod(contractName, contractMethod)

        then:
        method.getContractName() == contractName
        method.getContractMethod() == contractMethod
        List<CallMethod> callMethodList = method.getCallMethodList()
        callMethodList.size() == size
        CallMethod callMethod = callMethodList.get(0)
        callMethod.getContractName() == contractNameForCall
        callMethod.getContractMethod() == contractMethodForCall

        where:
        contractName             | contractMethod        || size | contractNameForCall  | contractMethodForCall
        "Validator"              | "addAccount"          || 1    | "FinancialValidator" | "hasValidatorRole"
        "Validator"              | "syncAccount"         || 1    | "FinancialToken"     | "checkSyncAccount"
        "Token"                  | "approve"             || 1    | "FinancialToken"     | "checkApprove"
        "JPYTokenTransferBridge" | "transfer"            || 1    | "FinancialToken"     | "checkExchange"
        "Validator"              | "setTerminated"       || 1    | "FinancialValidator" | "hasValidatorRole"
        "Token"                  | "mint"                || 1    | "FinancialAccount"   | "checkMint"
        "Token"                  | "burn"                || 1    | "FinancialAccount"   | "checkBurn"
        "Token"                  | "transferSingle"      || 1    | "FinancialToken"     | "checkTransaction"
        "Validator"              | "setAccountOpenState" || 1    | "FinancialToken"     | "checkAccountByRegion"
    }

    def "GetContractMethod: 取得したValidator_addAccountのremapArgsが正常に実行できること"() {
        when:
        CallRequiredSendMethod method = CallRequiredSendMethodInfo.getContractMethod(contractName, contractMethod)
        LinkedHashMap result = method.remapArgs(args)

        then:
        result.size() == 6
        result.get("validatorId") == 1
        result.get("accountId") == 2
        result.get("accountSignature") == 3
        result.get("info") == 4
        result.get("deadline") == 5
        result.get("signature") == 6

        where:
        contractName | contractMethod | args
        "Validator"  | "addAccount"   | new LinkedHashMap() {
            {
                put("validatorId", 1)
                put("accountId", 2)
                put("accountSignature", 3)
                put("info", 4)
                put("deadline", 5)
                put("signature", 6)
            }
        }
    }

    def "GetContractMethod: 取得したToken_approveのremapArgsが正常に実行できること"() {
        when:
        CallRequiredSendMethod method = CallRequiredSendMethodInfo.getContractMethod(contractName, contractMethod)
        LinkedHashMap result = method.remapArgs(args)

        then:
        result.size() == 8
        result.get("validatorId") == 1
        result.get("ownerId") == 2
        result.get("spenderId") == 3
        result.get("amount") == 4
        result.get("accountSignature") == 5
        result.get("info") == 6
        result.get("deadline") == 7
        result.get("signature") == 8

        where:
        contractName | contractMethod | args
        "Token"      | "approve"      | new LinkedHashMap() {
            {
                put("validatorId", 1)
                put("ownerId", 2)
                put("spenderId", 3)
                put("amount", 4)
                put("accountSignature", 5)
                put("info", 6)
                put("deadline", 7)
                put("signature", 8)
            }
        }
    }

    def "GetContractMethod: 取得したJPYTokenTransferBridge_transferのremapArgsが正常に実行できること"() {
        when:
        CallRequiredSendMethod method = CallRequiredSendMethodInfo.getContractMethod(contractName, contractMethod)
        LinkedHashMap result = method.remapArgs(args)

        then:
        result.size() == 10
        result.get("validatorId") == 1
        result.get("accountId") == 2
        result.get("fromTokenId") == 3
        result.get("toTokenId") == 4
        result.get("amount") == 5
        result.get("timeoutTimestamp") == 6
        result.get("accountSignature") == 7
        result.get("info") == 8
        result.get("deadline") == 9
        result.get("signature") == 10

        where:
        contractName             | contractMethod | args
        "JPYTokenTransferBridge" | "transfer"     | new LinkedHashMap() {
            {
                put("validatorId", 1)
                put("accountId", 2)
                put("fromTokenId", 3)
                put("toTokenId", 4)
                put("amount", 5)
                put("timeoutTimestamp", 6)
                put("accountSignature", 7)
                put("info", 8)
                put("deadline", 9)
                put("signature", 10)
            }
        }
    }
}
