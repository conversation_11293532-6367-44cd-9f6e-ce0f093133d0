package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.NoRevertReasonException
import spock.lang.Specification

class TransactionDatabaseSpec extends Specification {
    private static final String HASH_KEY = "0x00001"

    def "Has: beginからtoSuccessまでハッシュが存在すること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        boolean hasKeyInitially = transactionDatabase.has(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        boolean hasKeyAfterBegin = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        boolean hasKeyAfterQueued = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        boolean hasKeyAfterExecuted = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toSuccess(HASH_KEY)
        boolean hasKeyAfterSuccess = transactionDatabase.has(HASH_KEY)
        transactionDatabase.removeKey(HASH_KEY)
        boolean  hasKeyAfterRemove = transactionDatabase.has(HASH_KEY)

        then:
        hasKeyInitially == false
        hasKeyAfterBegin == true
        hasKeyAfterQueued == true
        hasKeyAfterExecuted == true
        hasKeyAfterSuccess == true
        hasKeyAfterRemove == false
    }

    def "Has: beginからtoRejectedまでハッシュが存在すること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        boolean hasKeyInitially = transactionDatabase.has(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        boolean hasKeyAfterBegin = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toRejected(HASH_KEY)
        boolean hasKeyAfterRejected = transactionDatabase.has(HASH_KEY)
        transactionDatabase.removeKey(HASH_KEY)
        boolean  hasKeyAfterRemove = transactionDatabase.has(HASH_KEY)

        then:
        hasKeyInitially == false
        hasKeyAfterBegin == true
        hasKeyAfterRejected == true
        hasKeyAfterRemove == false
    }

    def "Has: beginからtoRevertedまでハッシュが存在すること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        boolean hasKeyInitially = transactionDatabase.has(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        boolean hasKeyAfterBegin = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        boolean hasKeyAfterQueued = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        boolean hasKeyAfterExecuted = transactionDatabase.has(HASH_KEY)
        transactionDatabase.toReverted(HASH_KEY, "test")
        boolean hasKeyAfterReverted = transactionDatabase.has(HASH_KEY)
        transactionDatabase.removeKey(HASH_KEY)
        boolean  hasKeyAfterRemove = transactionDatabase.has(HASH_KEY)

        then:
        hasKeyInitially == false
        hasKeyAfterBegin == true
        hasKeyAfterQueued == true
        hasKeyAfterExecuted == true
        hasKeyAfterReverted == true
        hasKeyAfterRemove == false
    }

    def "GetState: beginからtoSuccessまでのステータスが正常に遷移していること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        TransactionState initialState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        TransactionState beginState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        TransactionState queuedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        TransactionState executedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toSuccess(HASH_KEY)
        TransactionState successState = transactionDatabase.getState(HASH_KEY)

        then:
        initialState == TransactionState.NONE
        beginState == TransactionState.INITIAL
        queuedState == TransactionState.QUEUED
        executedState == TransactionState.EXECUTED
        successState == TransactionState.SUCCESS
    }

    def "GetState: beginからtoRejectedまでのステータスが正常に遷移していること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        TransactionState initialState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        TransactionState beginState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toRejected(HASH_KEY)
        TransactionState rejectedState = transactionDatabase.getState(HASH_KEY)

        then:
        initialState == TransactionState.NONE
        beginState == TransactionState.INITIAL
        rejectedState == TransactionState.REJECTED
    }

    def "GetState: beginからtoRevertedまでのステータスが正常に遷移していること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        TransactionState initialState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        TransactionState beginState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        TransactionState queuedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        TransactionState executedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toReverted(HASH_KEY, "test")
        TransactionState revertedState = transactionDatabase.getState(HASH_KEY)

        then:
        initialState == TransactionState.NONE
        beginState == TransactionState.INITIAL
        queuedState == TransactionState.QUEUED
        executedState == TransactionState.EXECUTED
        revertedState == TransactionState.REVERTED
    }

    def "IsCompleted: beginからtoSuccessまでの完了状態が正常に判定されていること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        transactionDatabase.begin(HASH_KEY)
        boolean isCompletedAfterBegin = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        boolean isCompletedAfterQueued = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        boolean isCompletedAfterExecuted = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toSuccess(HASH_KEY)
        boolean isCompletedAfterSuccess = transactionDatabase.isCompleted(HASH_KEY)

        then:
        isCompletedAfterBegin == false
        isCompletedAfterQueued == false
        isCompletedAfterExecuted == false
        isCompletedAfterSuccess == true
    }

    def "IsCompleted: beginからtoRejectedまでの完了状態が正常に判定されていること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        transactionDatabase.begin(HASH_KEY)
        boolean isCompletedAfterBegin = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toRejected(HASH_KEY)
        boolean isCompletedAfterRejected = transactionDatabase.isCompleted(HASH_KEY)

        then:
        isCompletedAfterBegin == false
        isCompletedAfterRejected == false
    }

    def "IsCompleted: beginからtoRevertedまでの完了状態が正常に判定されていること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        transactionDatabase.begin(HASH_KEY)
        boolean isCompletedAfterBegin = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        boolean isCompletedAfterQueued = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        boolean isCompletedAfterExecuted = transactionDatabase.isCompleted(HASH_KEY)
        transactionDatabase.toReverted(HASH_KEY, "test")
        boolean isCompletedAfterReverted = transactionDatabase.isCompleted(HASH_KEY)

        then:
        isCompletedAfterBegin == false
        isCompletedAfterQueued == false
        isCompletedAfterExecuted == false
        isCompletedAfterReverted == true
    }

    def "GetRevertReason: RevertReasonが取得できること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        TransactionState initialState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        TransactionState beginState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        TransactionState queuedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        TransactionState executedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toReverted(HASH_KEY, "reason test")
        String reason = transactionDatabase.getRevertReason(HASH_KEY)

        then:
        reason == "reason test"
    }

    def "GetRevertReason: 空文字の場合にNoRevertReasonExceptionが発生すること"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase();
        TransactionState initialState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.begin(HASH_KEY)
        TransactionState beginState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toQueued(HASH_KEY)
        TransactionState queuedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toExecuted(HASH_KEY)
        TransactionState executedState = transactionDatabase.getState(HASH_KEY)
        transactionDatabase.toReverted(HASH_KEY, "")
        transactionDatabase.getRevertReason(HASH_KEY)

        then:
        thrown(NoRevertReasonException)
    }

    def "HashKeyが存在しない場合のエラー"() {
        when:
        method.call()

        then:
        thrown(ex)

        where:
        method                                                       || ex
        ({ new TransactionDatabase().isCompleted(HASH_KEY) })        || IllegalArgumentException
        ({ new TransactionDatabase().getRevertReason(HASH_KEY) })    || NoRevertReasonException
        ({ new TransactionDatabase().toRejected(HASH_KEY) }) || IllegalArgumentException
        ({ new TransactionDatabase().toQueued(HASH_KEY) }) || IllegalArgumentException
        ({ new TransactionDatabase().toExecuted(HASH_KEY) }) || IllegalArgumentException
        ({ new TransactionDatabase().toSuccess(HASH_KEY) }) || IllegalArgumentException
        ({ new TransactionDatabase().toReverted(HASH_KEY, "test") }) || IllegalArgumentException
    }

    def "RemoveKey: nullを渡してもエラーにならないこと"() {
        when:
        TransactionDatabase transactionDatabase = new TransactionDatabase()
        transactionDatabase.removeKey(null)

        then:
        notThrown(NullPointerException)
    }
}
