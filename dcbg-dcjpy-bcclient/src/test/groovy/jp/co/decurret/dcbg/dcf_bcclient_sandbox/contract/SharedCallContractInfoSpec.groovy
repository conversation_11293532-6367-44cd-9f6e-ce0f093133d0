package jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract

import spock.lang.Specification

class SharedCallContractInfoSpec extends Specification {
    def "Contains: 正常に判定ができること"() {
        when:
        boolean result = SharedCallContractInfo.contains(contractName)

        then:
        result == bool

        where:
        contractName         || bool
        "FinancialAccount"   || true
        "FinancialToken"     || true
        "FinancialValidator" || true
        "None"               || false
        "Token"              || false
    }
}
