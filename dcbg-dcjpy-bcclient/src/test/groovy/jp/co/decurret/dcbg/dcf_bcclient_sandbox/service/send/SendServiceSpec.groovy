package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInputParameter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractOutputParameter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ConvertedInputData
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.WebSocketConnectionPoolBase
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.SpyBean
import org.web3j.protocol.core.methods.response.EthCall
import org.web3j.protocol.core.methods.response.EthSendTransaction

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.doCallRealMethod
import static org.mockito.Mockito.doReturn
import static org.mockito.Mockito.when

class SendServiceSpec extends IntegrationTestBase {
    @SpyBean
    private TransactionDatabase transactionDatabase
    @SpyBean
    private ServiceInputConverter serviceInputConverter
    @Autowired
    private SendService sendService
    @SpyBean
    private EthRequestExecutor ethRequestExecutor
    @SpyBean
    private ContractInfo contractInfo

    private static final String TRANSACTION_HASH = "0x11"

    def "setup"() {
        EthSendTransaction ethSendTransaction = Mockito.mock(EthSendTransaction.class)
        when(ethSendTransaction.getError())
                .thenReturn(null)
        when(ethSendTransaction.getTransactionHash())
                .thenReturn(TRANSACTION_HASH)

        // doSend実行時にMock化したEthSendTransactionを返却する
        doReturn(ethSendTransaction)
                .when(ethRequestExecutor).doSend((String) notNull(), (String) notNull(), (WebSocketConnectionPoolBase) notNull())

        // transactionDatabaseのisCompletedは常にtrueを返す
        Mockito.doAnswer(new Answer<Boolean>() {
            @Override
            Boolean answer(InvocationOnMock invocation) throws Throwable {
                transactionDatabase.toExecuted(TRANSACTION_HASH)
                transactionDatabase.toSuccess(TRANSACTION_HASH)
                return (boolean) invocation.callRealMethod()
            }
        }).when(transactionDatabase).isCompleted(TRANSACTION_HASH)
    }

    def "Execute: SendServiceが正常に実行できること"() {
        when:
        SendServiceOutput sendServiceOutput = this.sendService.execute(new SendServiceInput(contractName, method, args))

        then:
        sendServiceOutput.isResult() == true
        sendServiceOutput.getError() == ""
        sendServiceOutput.getTransactionHash() != null
        sendServiceOutput.getTransactionHash() != ""

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Execute: コントラクト名・メソッド名が存在しない場合と引数不足の場合にBadRequestExceptionが発生すること"() {
        when:
        doCallRealMethod()
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        doReturn(new ContractABI("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.serviceInputConverter).extractExternalContractABI((ServiceInputBase) notNull())
        this.sendService.execute(new SendServiceInput(contractName, method, args))

        then:
        thrown(ex)

        where:
        contractName | method     | args | ex
        "none"       | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }                                | BadRequestException
        "Validator"  | "none"     | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }                                | BadRequestException
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
            }
        }                                | BadRequestException
    }

    def "DoCallCheckが実行されBadRequestExceptionが返ること"() {
        when:
        doReturn(new ContractFunction(method, new ArrayList<ContractInputParameter>(), new ArrayList<ContractOutputParameter>()))
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        doReturn(new ContractABI(contractName, "0x00", new ArrayList<ContractFunction>()))
                .when(this.contractInfo).getContractABI((String) notNull())
        doReturn(new ConvertedInputData("", ""))
                .when(this.serviceInputConverter).convert((ServiceInputBase) notNull(), (ContractFunction) notNull(), (String) notNull())

        EthCall ethCall = Mockito.mock(EthCall.class)
        when(ethCall.getValue()).thenReturn("")
        doReturn(ethCall)
                .when(ethRequestExecutor).doCall((String) notNull(), (String) notNull(), (WebSocketConnectionPoolBase) notNull())
        doReturn(new LinkedHashMap() {
            {
                put("err", "test error")
                put("success", false)
            }
        }).when(ethRequestExecutor).decodeCallResult((String) notNull(), (ContractFunction) notNull())

        SendServiceOutput output = this.sendService.execute(new SendServiceInput(contractName, method, args))

        then:
        output.isResult() == result
        output.getError() == error
        output.getTransactionHash() == transactionHash

        where:
        contractName             | method       | args                || result | error        | transactionHash
        "Token"                  | "approve"    | new LinkedHashMap() || false  | "test error" | ""
        "Validator"              | "addAccount" | new LinkedHashMap() {
            {
                put("sigAccount", "0x00")
            }
        }                                                             || false  | "test error" | ""
        "JPYTokenTransferBridge" | "transfer"   | new LinkedHashMap() || false  | "test error" | ""
    }

    def "DoCallCheckが実行され、コントラクトの結果からsuccessが取得できない場合、BlockchainIOExceptionが返ること"() {
        when:
        doReturn(new ContractFunction(method, new ArrayList<ContractInputParameter>(), new ArrayList<ContractOutputParameter>()))
                .when(this.serviceInputConverter).extractContractFunction((ServiceInputBase) notNull(), (ContractABI) notNull())
        doReturn(new ContractABI(contractName, "0x00", new ArrayList<ContractFunction>()))
                .when(this.contractInfo).getContractABI((String) notNull())
        doReturn(new ConvertedInputData("", ""))
                .when(this.serviceInputConverter).convert((ServiceInputBase) notNull(), (ContractFunction) notNull(), (String) notNull())

        EthCall ethCall = Mockito.mock(EthCall.class)
        when(ethCall.getValue()).thenReturn("")
        doReturn(ethCall)
                .when(ethRequestExecutor).doCall((String) notNull(), (String) notNull(), (WebSocketConnectionPoolBase) notNull())
        doReturn(new LinkedHashMap()).when(ethRequestExecutor).decodeCallResult((String) notNull(), (ContractFunction) notNull())

        SendServiceOutput output = this.sendService.execute(new SendServiceInput(contractName, method, args))

        then:
        thrown(BlockchainIOException)

        where:
        contractName             | method       | args
        "Token"                  | "approve"    | new LinkedHashMap()
    }
}
