package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.Application
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.IntegrationTestBase
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

import static org.mockito.Mockito.when

@ActiveProfiles("test")
@AutoConfigureMockMvc
class CustomHealthIndicatorSpec extends IntegrationTestBase {
    @MockBean
    private Application application
    @MockBean
    private ApplicationProperties applicationProperties
    @MockBean
    private ContractInfo contractInfo
    @MockBean
    private MainWebSocketConnectionPool mainWebSocketConnectionPool
    @MockBean
    private SubWebSocketConnectionPool subWebSocketConnectionPool
    @Autowired
    private MockMvc mockMvc;

    def "Health: WebSocketに接続されている場合にステータス200が返ってくること"() {
        when:
        when(applicationProperties.getUseSubWebSocket())
                .thenReturn(true)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isOk())
    }

    def "Health: MainWebSocketに接続されていない場合にステータス503が返ってくること"() {
        when:
        when(applicationProperties.getUseSubWebSocket())
                .thenReturn(true)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isServiceUnavailable())
    }

    def "Health: SubWebSocketに接続されていない場合にステータス503が返ってくること"() {
        when:
        when(applicationProperties.getUseSubWebSocket())
                .thenReturn(true)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isServiceUnavailable())
    }

    def "Health: SubWebSocketを使用しないときMainWebSocketが接続されている場合にステータス200が返ってくること"() {
        when:
        when(applicationProperties.getUseSubWebSocket())
                .thenReturn(false)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isOk())
    }

    def "Health: SubWebSocketを使用しないときMainWebSocketが接続されていない場合にステータス503が返ってくること"() {
        when:
        when(applicationProperties.getUseSubWebSocket())
                .thenReturn(false)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isServiceUnavailable())
    }
}
