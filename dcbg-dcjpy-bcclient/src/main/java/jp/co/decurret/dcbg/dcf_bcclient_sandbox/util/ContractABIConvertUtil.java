package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.*;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.AbiFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.web3j.abi.DefaultFunctionEncoder;
import org.web3j.abi.DefaultFunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ContractABIConvertUtil {

    private static final Pattern ABI_FILENAME_PATTERN = Pattern.compile("(?:.*/)?([^/]+)\\.json");

    /**
     * バイト文字列で入ってきたJSONをContractABIに変換する
     *
     * @param in バイト文字列のJSON
     * @return ContractABI
     * @throws IOException JSON解析に失敗した場合
     */
    public static ContractABI convertByteToObject(AbiFormat abiFormat, String key, byte[] in) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(in);
        List<ContractFunction> functionInfoList = new ArrayList<>();

        Matcher matcher = ABI_FILENAME_PATTERN.matcher(key);
        if (matcher.find() == false) {
            throw new RuntimeException("abi file key is unexpected format. key = " + key);
        }

        String contractName = matcher.group(1);

        String address;
        if (abiFormat == AbiFormat.HARDHAT) {
            address = rootNode.get("address").asText();
        } else if (abiFormat == AbiFormat.TRUFFLE) {
            // NOTE: Get address from the first chainId node.
            JsonNode networksNode = rootNode.get("networks");
            String chainId = networksNode.fieldNames().next();
            address = networksNode.get(chainId).get("address").asText();
        } else {
            throw new UnsupportedOperationException("AbiFormat is not supported : " + abiFormat);
        }

        JsonNode abi = rootNode.get("abi");
        for (JsonNode functionInfoNode : abi) {
            JsonNode functionNameNode = functionInfoNode.get("name");
            // nameがないものはスキップ
            if (functionNameNode == null) {
                continue;
            }
            String functionName = functionInfoNode.get("name").asText();

            // Functionインプット変換
            List<ContractInputParameter> inputParamList = convertNodeToParameter(
                    functionInfoNode.get("inputs"),
                    ContractABIConvertUtil::toInputParameter
            );
            // Functionアウトプット変換
            List<ContractOutputParameter> outputParamList = convertNodeToParameter(
                    functionInfoNode.get("outputs"),
                    ContractABIConvertUtil::toOutputParameter
            );
            functionInfoList.add(new ContractFunction(functionName, inputParamList, outputParamList));
        }

        log.info("contractName={}, address={}", contractName, address);
        return new ContractABI(contractName, address, functionInfoList);
    }

    /**
     * JsonNodeをParameterオブジェクトに変換する
     *
     * @param node     JsonNode
     * @param function 変換用の関数定義
     * @param <T>      ContractParameterBaseを継承したクラス
     * @return Parameterオブジェクト
     */
    private static <T extends ContractParameterBase> List<T> convertNodeToParameter(
            @Nullable JsonNode node,
            @NonNull java.util.function.Function<JsonNode, T> function
    ) {
        List<T> paramList = new ArrayList<>();
        if (node == null || node.isEmpty()) {
            return paramList;
        }
        node.forEach((n) -> {
            try {
                // 引数に指定された関数を実行する
                paramList.add(function.apply(n));
            } catch (UnsupportedOperationException e) {
                // NOTE: AbiTypesクラスのサポート外の型の場合はスルーする
                // 例 Unsupported type encountered: tuple
                log.error("Unsupported AbiType in converting json to java object", e);
            }
        });
        return paramList;
    }

    /**
     * JsonNodeをContractInputParameterに変換する
     *
     * @param parameterNode JsonNode
     * @return ContractInputParameter
     */
    private static ContractInputParameter toInputParameter(JsonNode parameterNode) {
        String paramName = parameterNode.get("name").asText();
        String paramTypeString = parameterNode.get("type").asText();
        return new ContractInputParameter(paramName, EthDataTypeUtil.toTypeClass(paramTypeString), paramTypeString);
    }

    /**
     * JsonNodeをContractOutputParameterに変換する
     *
     * @param parameterNode JsonNode
     * @return ContractOutputParameter
     */
    private static ContractOutputParameter toOutputParameter(JsonNode parameterNode) {
        String paramName = parameterNode.get("name").asText();
        String paramTypeString = parameterNode.get("type").asText();
        return new ContractOutputParameter(paramName, EthDataTypeUtil.toTypeClass(paramTypeString), paramTypeString);
    }

    /**
     * Functionをエンコードする
     *
     * @param contractFunctionInfo コントラクトファンクション
     * @param inputParams          パラメータリスト
     * @return エンコードされた文字列
     * @throws Exception エンコードに失敗した場合
     */
    public static String encodeFunction(ContractFunction contractFunctionInfo, List<Object> inputParams) throws Exception {
        log.info("Function ({}) encoding started.", contractFunctionInfo.getName());
        List<ContractInputParameter> inputParameterList = contractFunctionInfo.getInputParameterList();
        if (inputParams.size() < inputParameterList.size()) {
            throw new IllegalStateException("params.length is less than paramTypeList.size(). params.size()=" + inputParams.size() + ", paramTypeList.size()=" + inputParameterList.size());
        }

        List<Type> inputParamObjList = new ArrayList<>();
        for (int i = 0; i < inputParameterList.size(); i++) {
            Object inputParamObj = inputParams.get(i);
            ContractInputParameter inputParameter = inputParameterList.get(i);
            log.debug("input parameter[{}]: name={}, type={}, value={}.", i, inputParameter.getName(), inputParameter.getTypeString(), inputParamObj);
            inputParamObjList.add(constructTypeObject(inputParameter, inputParamObj));
        }
        List<TypeReference<?>> outputParamTypeRefList = new ArrayList<>();
        for (ContractOutputParameter outputParam : contractFunctionInfo.getOutputParameterList()) {
            outputParamTypeRefList.add(EthDataTypeUtil.toTypeReference(outputParam.getTypeString()));
        }
        DefaultFunctionEncoder encoder = new DefaultFunctionEncoder();
        String encoded = encoder.encodeFunction(new Function(contractFunctionInfo.getName(), inputParamObjList, outputParamTypeRefList));
        log.info("Function ({}) encoding finished.", contractFunctionInfo.getName());
        return encoded;
    }

    /**
     * AbiTypeインスタンスを生成する
     *
     * @param inputParameter ContractInputParameter
     * @param o              Value
     * @return AbiTypeインスタンス
     * @throws Exception 対応するコンストラクタが存在しない場合
     */
    private static Type constructTypeObject(ContractInputParameter inputParameter, Object o) throws Exception {
        Class<Type> paramType = inputParameter.getClazz();
        Constructor<Type> constructor;
        if (EthDataTypeUtil.isArray(inputParameter.getTypeString())) {
            // DynamicArrayの場合は内部クラスを変換する
            String basicTypeString = EthDataTypeUtil.toBasicTypeString(inputParameter.getTypeString());
            Class<Type> basicType = EthDataTypeUtil.toTypeClass(basicTypeString);
            List<Object> params = (List) o;

            if (params.size() == 0) {
                // 0件の場合は以降の処理なし
                return DynamicArray.empty(basicTypeString);
            }
            List<Type> values = new ArrayList<>();
            Constructor<Type> basicConstructor = basicType.getConstructor(EthDataTypeUtil.toEthParamObject(basicTypeString, params.get(0)).getClass());
            for (Object p : params) {
                values.add(basicConstructor.newInstance(EthDataTypeUtil.toEthParamObject(basicTypeString, p)));
            }
            return new DynamicArray(basicType, values);
        }
        constructor = paramType.getConstructor(o.getClass());
        return constructor.newInstance(o);
    }

    /**
     * Functionをデコードする
     *
     * @param contractFunctionInfo コントラクトファンクション
     * @param rawInput             エンコードされた文字列
     * @return AbiDatatypeのリスト
     */
    public static List<Type> decodeFunction(ContractFunction contractFunctionInfo, String rawInput) {
        log.info("Function ({}) decoding started.", contractFunctionInfo.getName());
        List<TypeReference<Type>> outputTypeRefList = new ArrayList<>();
        for (ContractOutputParameter outputParam : contractFunctionInfo.getOutputParameterList()) {
            TypeReference<Type> outputTypeRef = EthDataTypeUtil.toTypeReference(outputParam.getTypeString());
            outputTypeRefList.add(outputTypeRef);
        }
        DefaultFunctionReturnDecoder decoder = new DefaultFunctionReturnDecoder();
        List<Type> decoded = decoder.decodeFunctionResult(rawInput, outputTypeRefList);
        log.info("Function ({}) decoding finished.", contractFunctionInfo.getName());
        return decoded;
    }
}
