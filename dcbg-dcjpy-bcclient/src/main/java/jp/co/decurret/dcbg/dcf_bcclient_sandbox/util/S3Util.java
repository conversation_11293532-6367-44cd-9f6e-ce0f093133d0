package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.LocalProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * S3操作用の汎用クラス
 */
@Component
@Slf4j
public class S3Util {
    private final ApplicationProperties applicationProperties;
    private final LocalProperties localProperties;

    @Autowired
    public S3Util(ApplicationProperties applicationProperties, LocalProperties localProperties) {
        this.applicationProperties = applicationProperties;
        this.localProperties = localProperties;
    }

    /**
     * 設定に応じてS3Clientを構築し返却する
     *
     * @return S3Client
     */
    @NonNull
    private S3Client getS3Client() {
        Region region = Region.of(applicationProperties.getRegion());
        return S3Client.builder()
                .region(region)
                .build();
    }

    /**
     * ローカルS3を使用する
     *
     * @return true: 使用する
     */
    private boolean isLocal() {
        Optional<Boolean> useLocalS3Bucket = Optional.ofNullable(localProperties.getUseLocalS3Bucket());
        return useLocalS3Bucket.orElse(false);
    }

    /**
     * ローカル用のS3Clientを構築し返却する
     *
     * @return S3Client
     */
    private S3Client getLocalS3Client(boolean isExternalABIBucket) {
        Region region = Region.of(applicationProperties.getRegion());
        String localS3BucketUri = isExternalABIBucket ? localProperties.getLocalExternalS3Uri() : localProperties.getLocalS3Uri();
        AwsCredentials credentials = isExternalABIBucket ?
                AwsBasicCredentials.create(localProperties.getExternalS3AccessKey(), localProperties.getExternalS3SecretKey())
                : AwsBasicCredentials.create(localProperties.getS3AccessKey(), localProperties.getS3SecretKey());

        log.debug("use local S3 bucket: {}", localS3BucketUri);
        try {
            return S3Client.builder()
                    .region(region)
                    .endpointOverride(new URI(localS3BucketUri))
                    .credentialsProvider(StaticCredentialsProvider.create(credentials))
                    .build();
        } catch (URISyntaxException e) {
            log.error("invalid local S3 URI", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 指定したバケットのオベジェクト一覧を取得する
     *
     * @param bucketName バケット名
     * @return ListObjectsV2Response
     */
    @NonNull
    public ListObjectsV2Response listObjects(@NonNull String bucketName) {
        S3Client s3Client = this.isLocal() ? this.getLocalS3Client(false) : this.getS3Client();
        try {
            ListObjectsV2Request request = ListObjectsV2Request.builder().bucket(bucketName).build();
            return s3Client.listObjectsV2(request);
        } finally {
            s3Client.close();
        }

    }

    /**
     * 指定したバケットの複数オブジェクトをバイト配列で取得する
     *
     * @param keys       複数オベジェクトキー
     * @param bucketName バケット名
     * @return ファイル内容のbyte配列リスト
     */
    @NonNull
    public List<byte[]> getAllObjectsAsByteArrayList(@NonNull List<String> keys, @NonNull String bucketName) {
        S3Client s3Client = this.isLocal() ? this.getLocalS3Client(false) : this.getS3Client();
        try {
            return keys.stream().map((key) -> {
                GetObjectRequest objectRequest = GetObjectRequest
                        .builder()
                        .key(key)
                        .bucket(bucketName)
                        .build();
                return s3Client.getObjectAsBytes(objectRequest).asByteArray();
            }).collect(Collectors.toList());
        } finally {
            s3Client.close();
        }
    }

    /**
     * 指定したバケットのオブジェクトをバイト配列で取得する
     *
     * @param key        オベジェクトキー
     * @param bucketName バケット名
     * @return ファイル内容のbyte配列
     */
    @NonNull
    public byte[] getObjectAsByte(@NonNull String key, @NonNull String bucketName) {
        S3Client s3Client = this.isLocal() ? this.getLocalS3Client(true) : this.getS3Client();
        try {
            GetObjectRequest objectRequest = GetObjectRequest
                    .builder()
                    .key(key)
                    .bucket(bucketName)
                    .build();
            return s3Client.getObjectAsBytes(objectRequest).asByteArray();
        } finally {
            s3Client.close();
        }
    }
}
