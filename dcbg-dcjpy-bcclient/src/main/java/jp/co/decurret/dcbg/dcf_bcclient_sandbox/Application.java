package jp.co.decurret.dcbg.dcf_bcclient_sandbox;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.SQSProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.TransferProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.subscribe.ISubscribeService;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer.ITransferPeriodicExecutionService;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.reflect.Field;
import java.util.Optional;

@Slf4j
@EnableScheduling
@SpringBootApplication
public class Application {
    private final ApplicationProperties applicationProperties;
    private final SQSProperties sqsProperties;
    private final TransferProperties transferProperties;
    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;
    private final SubWebSocketConnectionPool subWebSocketConnectionPool;
    private final ISubscribeService subscribeService;
    private final ITransferPeriodicExecutionService transferPeriodicExecutionService;

    @Autowired
    public Application(
            ApplicationProperties applicationProperties,
            SQSProperties sqsProperties,
            TransferProperties transferProperties,
            MainWebSocketConnectionPool mainWebSocketConnectionPool,
            SubWebSocketConnectionPool subWebSocketConnectionPool,
            ISubscribeService subscribeService,
            ITransferPeriodicExecutionService transferPeriodicExecutionService
    ) {
        this.applicationProperties = applicationProperties;
        this.sqsProperties = sqsProperties;
        this.transferProperties = transferProperties;
        this.mainWebSocketConnectionPool = mainWebSocketConnectionPool;
        this.subWebSocketConnectionPool = subWebSocketConnectionPool;
        this.subscribeService = subscribeService;
        this.transferPeriodicExecutionService = transferPeriodicExecutionService;
    }

    public static void main(String[] args) {
        // API Server
        SpringApplication.run(Application.class, args);
    }

    @PostConstruct
    private void init() throws IllegalAccessException {
        this.printHeapMemory();
        this.printApplicationProperties();
    }

    /**
     * 定期的にNewHeadsの購読状態を確認し未接続状態の場合は購読を開始する
     * 初期起動時と指定したインターバル毎に実行される
     */
    @Scheduled(fixedDelayString = "${app.subscriptionCheckInterval}")
    @Synchronized
    private void subscribeNewHeads() {
        // メイン側
        if (this.mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed()) {
            this.mainWebSocketConnectionPool.setNewHeadsSubscription(
                    this.subscribeService.execute(
                            this.mainWebSocketConnectionPool,
                            this.applicationProperties.getWebSocketUriHost(),
                            this.applicationProperties.getWebSocketUriPort(),
                            true
                    )
            );
        }
        // サブ側
        Optional<Boolean> useSubWebSocket = Optional.ofNullable(this.applicationProperties.getUseSubWebSocket());
        if (useSubWebSocket.orElse(false) && this.subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed()) {
            this.subWebSocketConnectionPool.setNewHeadsSubscription(this.subscribeService.execute(
                    this.subWebSocketConnectionPool,
                    this.applicationProperties.getSubWebSocketUriHost(),
                    this.applicationProperties.getSubWebSocketUriPort(),
                    false // サブ領域側はトランザクション結果を確認する必要がないのでブロックチェックを実施しない
            ));
        }
    }

    /**
     * NewHeadsの購読状態を定期的に確認し未接続状態の場合は購読を開始する
     * 指定したインターバル毎に実行される
     */
    // transferのバッチ処理を停止。TODO: https://decurret.atlassian.net/browse/DCFC-6118 にて対応する
//    @Scheduled(fixedDelayString = "${transfer.sendTransferInterval}")
//    @Synchronized
//    private void executeSendTransfer() {
//        this.transferPeriodicExecutionService.execute();
//    }

    /**
     * HeapMemory設定を出力する
     */
    private void printHeapMemory() {
        int mb = 1024 * 1024;
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long xms = memoryBean.getHeapMemoryUsage().getInit() / mb;
        long xmx = memoryBean.getHeapMemoryUsage().getMax() / mb;
        log.info("Heap Initial (xms) : " + xms + "mb");
        log.info("Heap Max (xmx) : " + xmx + "mb");
    }

    /**
     * アプリケーションプロパティ設定を出力する
     */
    private void printApplicationProperties() throws IllegalAccessException {
        log.info("[application properties: start]");
        for(Field field : this.applicationProperties.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            log.info("{}: {}", field.getName(), field.get(this.applicationProperties));
            field.setAccessible(false);
        }
        log.info("[application properties: end]");

        log.info("[sqs properties: start]");
        for(Field field : this.sqsProperties.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            log.info("{}: {}", field.getName(), field.get(this.sqsProperties));
            field.setAccessible(false);
        }
        log.info("[sqs properties: end]");

        log.info("[transfer properties: start]");
        for(Field field : this.transferProperties.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            log.info("{}: {}", field.getName(), field.get(this.transferProperties));
            field.setAccessible(false);
        }
        log.info("[transfer properties: end]");
    }
}
