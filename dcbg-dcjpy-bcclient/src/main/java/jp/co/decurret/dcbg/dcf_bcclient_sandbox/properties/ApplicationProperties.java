package jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix="app")
public class ApplicationProperties {

    private AbiFormat abiFormat;

    // メインWebSocket
    private String webSocketUriHost;
    private String webSocketUriPort;

    // サブWebSocket
    private Boolean useSubWebSocket;
    private String subWebSocketUriHost;
    private String subWebSocketUriPort;
    private Long requestTimeoutSec;
    private String region;
    private String contractFileBucketName;
    private String externalContractFileBucketName;
    private Long gasLimit;

    public void setAbiFormat(String abiFormat) {
        this.abiFormat = AbiFormat.get(abiFormat);
    }
}