package jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.web3j.protocol.Web3j;

@Slf4j
@Component
public class SubWebSocketConnectionPool extends WebSocketConnectionPoolBase {
    @Override
    public Web3j createWebSocketConnection(@NonNull String host, @NonNull String port, @NonNull boolean useSecureConnection) {
        Web3j web3j = super.createWebSocketConnection(host, port, useSecureConnection);
        log.info("Sub WebSocket connection pool is created.");
        return web3j;
    }
}
