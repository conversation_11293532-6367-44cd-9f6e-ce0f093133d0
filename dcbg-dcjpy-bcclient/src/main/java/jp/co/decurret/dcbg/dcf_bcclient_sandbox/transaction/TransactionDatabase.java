package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.NoRevertReasonException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * トランザクション状態管理クラス
 */
@Slf4j
@Component
public class TransactionDatabase {
    /**
     * トランザクション状態管理 (key: transactionHash)
     */
    private final Map<String, TransactionState> states = new ConcurrentHashMap<>();
    /**
     * RevertReasons管理 (key: transactionHash)
     */
    private final Map<String, String> revertReasons = new ConcurrentHashMap<>();

    /**
     * ハッシュが存在するか判定する
     *
     * @param transactionHash トランザクションハッシュ
     * @return 存在する場合: true
     */
    @NonNull
    public boolean has(@NonNull String transactionHash) {
        return this.states.containsKey(transactionHash);
    }

    /**
     * 指定したトランザクションハッシュの状態を取得する
     *
     * @param transactionHash トランザクションハッシュ
     * @return TransactionState
     */
    @NonNull
    public TransactionState getState(@NonNull String transactionHash) {
        if (!this.has(transactionHash)) {
            return TransactionState.NONE;
        }
        return this.states.get(transactionHash);
    }

    /**
     * 指定したトランザクションハッシュの完了状態を取得する
     *
     * @param transactionHash トランザクションハッシュ
     * @return 完了している場合: true
     */
    @NonNull
    public boolean isCompleted(@NonNull String transactionHash) {
        if (!this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash does not exist in TransactionDatabase.");
        }
        TransactionState state = this.getState(transactionHash);
        return state == TransactionState.SUCCESS || state == TransactionState.REVERTED;
    }

    /**
     * 指定したトランザクションハッシュのRevertReasonを取得する
     *
     * @param transactionHash トランザクションハッシュ
     * @return RevertReason
     */
    @NonNull
    public String getRevertReason(@NonNull String transactionHash) {
        if (!this.revertReasons.containsKey(transactionHash)) {
            throw new NoRevertReasonException("transactionReceipt does not have a revert reason.");
        }
        return this.revertReasons.get(transactionHash);
    }

    /**
     * transaction to initial state
     *
     * @param transactionHash トランザクションハッシュ
     */
    public void begin(@NonNull String transactionHash) {
        log.info("Begin transactionHash: {}", transactionHash);
        if (this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash already exists in TransactionDatabase.");
        }
        this.states.put(transactionHash, TransactionState.INITIAL);
    }

    /**
     * transaction to rejected state
     *
     * @param transactionHash トランザクションハッシュ
     */
    public void toRejected(@NonNull String transactionHash) {
        log.info("ToRejected transactionHash: {}", transactionHash);
        if (!this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash does not exist in TransactionDatabase.");
        }
        if (this.getState(transactionHash) != TransactionState.INITIAL) {
            throw new RuntimeException("TransactionState must be INITIAL");
        }
        this.states.put(transactionHash, TransactionState.REJECTED);
    }

    /**
     * transaction to queued state
     *
     * @param transactionHash トランザクションハッシュ
     */
    public void toQueued(@NonNull String transactionHash) {
        log.info("ToQueued transactionHash: {}", transactionHash);
        if (!this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash does not exist in TransactionDatabase.");
        }
        if (this.getState(transactionHash) != TransactionState.INITIAL) {
            throw new RuntimeException("TransactionState must be INITIAL");
        }
        this.states.put(transactionHash, TransactionState.QUEUED);
    }

    /**
     * transaction to executed state
     *
     * @param transactionHash トランザクションハッシュ
     */
    public void toExecuted(@NonNull String transactionHash) {
        log.info("ToExecuted transactionHash: {}", transactionHash);
        if (!this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash does not exist in TransactionDatabase.");
        }
        if (this.getState(transactionHash) != TransactionState.QUEUED) {
            throw new RuntimeException("TransactionState must be QUEUED");
        }
        states.put(transactionHash, TransactionState.EXECUTED);
    }

    /**
     * transaction to success state
     *
     * @param transactionHash トランザクションハッシュ
     */
    public void toSuccess(@NonNull String transactionHash) {
        log.info("ToSuccess transactionHash: {}", transactionHash);
        if (!this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash does not exist in TransactionDatabase.");
        }
        if (this.getState(transactionHash) != TransactionState.EXECUTED) {
            throw new RuntimeException("TransactionState must be EXECUTED");
        }
        this.states.put(transactionHash, TransactionState.SUCCESS);
    }

    /**
     * transaction to reverted state
     *
     * @param transactionHash トランザクションハッシュ
     * @param revertReason    Revert Reason
     */
    public void toReverted(@NonNull String transactionHash, @NonNull String revertReason) {
        log.info("ToReverted transactionHash: {}, revert reason: {}", transactionHash, revertReason);
        if (!this.has(transactionHash)) {
            throw new IllegalArgumentException("transactionHash does not exist in TransactionDatabase.");
        }
        this.states.put(transactionHash, TransactionState.REVERTED);

        if (!ObjectUtils.isEmpty(revertReason)) {
            this.revertReasons.put(transactionHash, revertReason);
        }
    }

    /**
     * 指定したトランザクションハッシュの状態管理を削除する
     *
     * @param transactionHash トランザクションハッシュ
     */
    public void removeKey(@Nullable String transactionHash) {
        if (transactionHash == null) {
            return;
        }
        log.info("Removed transactionHash: {}", transactionHash);
        this.states.remove(transactionHash);
    }
}
