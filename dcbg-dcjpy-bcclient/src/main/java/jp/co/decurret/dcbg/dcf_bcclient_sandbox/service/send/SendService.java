package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract.CallMethod;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract.CallRequiredSendMethod;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract.CallRequiredSendMethodInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ConvertedInputData;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.EthSendTransaction;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
@Slf4j
public class SendService implements ISendService {
    private final ContractInfo contractInfo;
    private final ServiceInputConverter serviceInputConverter;
    private final TransactionDatabase transactionDatabase;
    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;
    private final SubWebSocketConnectionPool subWebSocketConnectionPool;
    private final EthRequestExecutor ethRequestExecutor;

    @Autowired
    public SendService(
            ContractInfo contractInfo,
            ServiceInputConverter serviceInputConverter,
            TransactionDatabase transactionDatabase,
            MainWebSocketConnectionPool mainWebSocketConnectionPool,
            SubWebSocketConnectionPool subWebSocketConnectionPool,
            EthRequestExecutor ethRequestExecutor
    ) {
        this.contractInfo = contractInfo;
        this.serviceInputConverter = serviceInputConverter;
        this.transactionDatabase = transactionDatabase;
        this.mainWebSocketConnectionPool = mainWebSocketConnectionPool;
        this.subWebSocketConnectionPool = subWebSocketConnectionPool;
        this.ethRequestExecutor = ethRequestExecutor;
    }

    /**
     * SendServiceを実行する
     *
     * @param sendServiceInput サービスインプット
     * @return SendServiceOutput
     */
    @NonNull
    @Override
    public SendServiceOutput execute(@NonNull SendServiceInput sendServiceInput) {
        // input変換
        ContractABI contractABI;
        ContractFunction contractFunction;
        try {
            contractABI = contractInfo.getContractABI(sendServiceInput.getContractName());
            contractFunction = this.serviceInputConverter.extractContractFunction(sendServiceInput, contractABI);
        } catch (ContractNotFoundException e) {
            // 外部ABIから変換する
            log.info("No matching internal contracts.");
            try {
                contractABI = this.serviceInputConverter.extractExternalContractABI(sendServiceInput);
                contractFunction = this.serviceInputConverter.extractContractFunction(sendServiceInput, contractABI);
            } catch (ContractNotFoundException contractNotFoundException) {
                throw new BadRequestException("contractName not found");
            }
        }
        ConvertedInputData convertedInputData = this.serviceInputConverter.convert(sendServiceInput, contractFunction, contractABI.getAddress());

        // メソッド名が一致した場合にCallする
        if (CallRequiredSendMethodInfo.contains(sendServiceInput.getContractName(), sendServiceInput.getMethod())) {
            log.info("doCallCheck before send transaction ({}#{}).", sendServiceInput.getContractName(), sendServiceInput.getMethod());
            List<Future<LinkedHashMap>> callResult = this.doCallCheck(sendServiceInput);
            for (Future<LinkedHashMap> result : callResult) {
                try {
                    LinkedHashMap r = result.get();
                    Object success =  r.get("success");
                    if (success == null ) {
                        throw new BlockchainIOException("callCheck success not found");
                    }
                    if (Boolean.FALSE.equals(success)) {
                        log.info("call result: NG");
                        return new SendServiceOutput(false, (String) r.get("err"), "");
                    }
                } catch (InterruptedException e) {
                    throw new IllegalStateException("callCheck interrupted.", e);
                } catch (ExecutionException e) {
                    throw new IllegalStateException("failed to send call request.", e);
                }
            }
            log.info("call result: OK");
        }
        // send
        EthSendTransaction ethSendTransaction;
        try {
            log.info("doSend: contractName={}, method={}.", sendServiceInput.getContractName(), sendServiceInput.getMethod());
            ethSendTransaction = this.ethRequestExecutor.doSend(
                    convertedInputData.getContractAddress(),
                    convertedInputData.getEncodedFunction(),
                    this.mainWebSocketConnectionPool
            );
        } catch (IOException e) {
            log.error("Send request failed.", e);
            throw new BlockchainIOException("ethSendRawTransaction failed", e);
        }
        // トランザクション結果取得
        String transactionHash = ethSendTransaction.getTransactionHash();
        try {
            return this.ethRequestExecutor.fetchTransactionResult(ethSendTransaction);
        } finally {
            this.transactionDatabase.removeKey(transactionHash);
        }
    }

    /**
     * Sendリクエストを事前に検証するためにCallリクエストを実行する
     *
     * @param sendServiceInput Sendサービスインプット
     * @return Call結果内容
     */
    @NonNull
    List<Future<LinkedHashMap>> doCallCheck(@NonNull SendServiceInput sendServiceInput) {
        // コントラクト名とメソッド名を書き換えて共有DLTで検証する
        CallRequiredSendMethod method =
                CallRequiredSendMethodInfo.getContractMethod(sendServiceInput.getContractName(), sendServiceInput.getMethod());

        List<Future<LinkedHashMap>> futures = new ArrayList<>();
        ExecutorService threadService = Executors.newFixedThreadPool(method.getCallMethodList().size());
        for (CallMethod callMethod : method.getCallMethodList()) {
            Future<LinkedHashMap> future = threadService.submit(() -> {
                try {
                    CallServiceInput callServiceInput = new CallServiceInput(
                            callMethod.getContractName(),
                            callMethod.getContractMethod(),
                            method.remapArgs(sendServiceInput.getArgs())
                    );
                    log.info("doCallCheck: contractName={}, method={}, args={}.",
                            callServiceInput.getContractName(), callServiceInput.getMethod(), callServiceInput.getArgs());
                    ContractABI contractABI = contractInfo.getContractABI(callServiceInput.getContractName());
                    ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI);
                    ConvertedInputData convertedInputData = this.serviceInputConverter.convert(callServiceInput, contractFunction, contractABI.getAddress());
                    EthCall ethCall = this.ethRequestExecutor.doCall(
                            convertedInputData.getContractAddress(),
                            convertedInputData.getEncodedFunction(),
                            this.subWebSocketConnectionPool // 共有DLT側のコネクションを使用する
                    );
                    return this.ethRequestExecutor.decodeCallResult(ethCall.getValue(), contractFunction);
                } catch (IOException e) {
                    log.error("doCallCheck failed.", e);
                    throw new BlockchainIOException("failed to ethCall", e);
                }
            });
            futures.add(future);
        }
        threadService.shutdown();
        return futures;
    }
}
