package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class CustomHealthIndicator implements HealthIndicator {
    private ApplicationProperties applicationProperties;
    private MainWebSocketConnectionPool mainWebSocketConnectionPool;
    private SubWebSocketConnectionPool subWebSocketConnectionPool;

    @Autowired
    public CustomHealthIndicator(
            ApplicationProperties applicationProperties,
            MainWebSocketConnectionPool mainWebSocketConnectionPool,
            SubWebSocketConnectionPool subWebSocketConnectionPool
    ) {
        this.applicationProperties = applicationProperties;
        this.mainWebSocketConnectionPool = mainWebSocketConnectionPool;
        this.subWebSocketConnectionPool = subWebSocketConnectionPool;
    }

    /**
     * メイン領域とサブ領域のWebSocket接続状態を確認し少なくとも一方が接続されていない場合にDownを返却する
     *
     * @return Health
     */
    @Override
    public Health health() {
        if (this.mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed()) {
            return Health.down().build();
        }
        Optional<Boolean> useSubWebSocket = Optional.ofNullable(this.applicationProperties.getUseSubWebSocket());
        if (useSubWebSocket.orElse(false) && this.subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed()) {
            return Health.down().build();
        }
        return Health.up().build();
    }
}
