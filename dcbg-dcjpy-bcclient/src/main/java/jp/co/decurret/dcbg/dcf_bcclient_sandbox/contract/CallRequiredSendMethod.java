package jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public class CallRequiredSendMethod {
    private final String contractName;
    private final String contractMethod;
    private final List<CallMethod> callMethodList;
    // key: 変換前のパラメータ名、value: 変換後のパラメータ名
    private final Map<String, String> parameterMap;

    @NonNull
    public LinkedHashMap remapArgs(@NonNull LinkedHashMap args) {
        LinkedHashMap cloned = (LinkedHashMap) args.clone();
        for (Object key : args.keySet()) {
            if (this.parameterMap.containsKey(key)) {
                // ValueはそのままでマッピングするKeyを入れ替えた後に元のKeyを削除する
                cloned.put(this.parameterMap.get(key), cloned.get(key));
                cloned.remove(key);
            }
        }
        return cloned;
    }
}
