package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.LocalProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SQSUtil {
    private final ApplicationProperties applicationProperties;
    private final LocalProperties localProperties;

    private SqsClient sqsClient;

    @Autowired
    public SQSUtil(ApplicationProperties applicationProperties, LocalProperties localProperties) {
        this.applicationProperties = applicationProperties;
        this.localProperties = localProperties;
    }

    /**
     * 初回呼び出し時はSqsClientを新規作成しキャッシュした後に返却する。
     * 2回目以降はSqsClientのキャッシュを返却する。
     *
     * @return SqsClient
     */
    @NonNull
    private SqsClient getSqsClient() {
        if (Objects.nonNull(this.sqsClient)) {
            // クライアントのキャッシュを返す
            return this.sqsClient;
        }
        Region region = Region.of(applicationProperties.getRegion());
        Optional<Boolean> useLocalSqs = Optional.ofNullable(localProperties.getUseLocalSqs());
        if (useLocalSqs.orElse(false)) {
            // ローカルSQSを使用する場合
            String localSqsUri = localProperties.getLocalSqsUri();
            AwsCredentials credentials = AwsBasicCredentials.create(localProperties.getSqsAccessKey(), localProperties.getSqsSecretKey());
            try {
                this.sqsClient = SqsClient.builder()
                        .region(region)
                        .endpointOverride(new URI(localSqsUri))
                        .credentialsProvider(StaticCredentialsProvider.create(credentials))
                        .build();
            } catch (URISyntaxException e) {
                log.error("invalid local SQS URI", e);
                throw new RuntimeException(e);
            }
        } else {
            this.sqsClient = SqsClient.builder()
                    .region(region)
                    .build();
        }
        return this.sqsClient;
    }

    /**
     * 指定したキューにメッセージを送信する
     *
     * @param message                メッセージ
     * @param queueUri               キューURI
     * @param messageGroupId         メッセージグループID
     * @param messageDeduplicationId メッセージ重複排除ID
     * @return SendMessageResponse
     */
    @NonNull
    public SendMessageResponse sendMessage(
            @NonNull String message,
            @NonNull String queueUri,
            @NonNull String messageGroupId,
            @NonNull String messageDeduplicationId) {
        return this.getSqsClient().sendMessage(
                SendMessageRequest.builder()
                        .queueUrl(queueUri)
                        .messageBody(message)
                        .messageGroupId(messageGroupId) // https://docs.aws.amazon.com/ja_jp/AWSSimpleQueueService/latest/SQSDeveloperGuide/using-messagegroupid-property.html
                        .messageDeduplicationId(messageDeduplicationId) // https://docs.aws.amazon.com/ja_jp/AWSSimpleQueueService/latest/SQSDeveloperGuide/using-messagededuplicationid-property.html
                        .build());
    }

    /**
     * 指定したキューからメッセージを取得する
     *
     * @param maxNumberOfMessage 取得する最大メッセージする
     * @param queueUri           キューURI
     * @return List<Message>
     */
    @NonNull
    public List<Message> receiveMessage(@NonNull Integer maxNumberOfMessage, @NonNull String queueUri) {
        return this.getSqsClient().receiveMessage(ReceiveMessageRequest.builder()
                .queueUrl(queueUri)
                .maxNumberOfMessages(maxNumberOfMessage)
                .build()).messages();
    }

    /**
     * 指定したキューのメッセージを削除する
     *
     * @param messages メッセージ
     * @param queueUri キューURI
     * @return DeleteMessageBatchResponse
     */
    @NonNull
    public DeleteMessageBatchResponse deleteMessageBatch(@NonNull List<Message> messages, @NonNull String queueUri) {
        List<DeleteMessageBatchRequestEntry> requestEntries =
                messages.stream()
                        .map(m -> DeleteMessageBatchRequestEntry.builder()
                                .id(m.messageId())
                                .receiptHandle(m.receiptHandle()).build())
                        .collect(Collectors.toList());
        return this.getSqsClient().deleteMessageBatch(DeleteMessageBatchRequest.builder()
                .queueUrl(queueUri)
                .entries(requestEntries)
                .build());
    }
}
