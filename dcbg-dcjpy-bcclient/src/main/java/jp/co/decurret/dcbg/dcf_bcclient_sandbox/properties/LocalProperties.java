package jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix="local")
public class LocalProperties {
    private Boolean useLocalS3Bucket;
    private String localS3Uri;
    private String s3AccessKey;
    private String s3SecretKey;
    private String localExternalS3Uri;
    private String externalS3AccessKey;
    private String externalS3SecretKey;
    private Boolean useLocalSqs;
    private String localSqsUri;
    private String sqsAccessKey;
    private String sqsSecretKey;
}
