package jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;

/**
 * リクエスト前にCallリクエストを送信する必要があるSendメソッド情報
 */
@Slf4j
public class CallRequiredSendMethodInfo {
    /**
     * key: "コントラクト名#メソッド名"
     */
    private static final Map<String, CallRequiredSendMethod> methodMap = new LinkedHashMap<>();

    private static final String DELIMITER = "#";

    static {
        List<CallRequiredSendMethod> methodList = new ArrayList();
        methodList.add(new CallRequiredSendMethod(
                "Validator",
                "addAccount",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialValidator", "hasValidatorRole"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Validator",
                "syncAccount",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialToken", "checkSyncAccount"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Token",
                "approve",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialToken", "checkApprove"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "JPYTokenTransferBridge",
                "transfer",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialToken", "checkExchange"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Validator",
                "setTerminated",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialValidator", "hasValidatorRole"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Token",
                "mint",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialAccount", "checkMint"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Token",
                "burn",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialAccount", "checkBurn"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Token",
                "transferSingle",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialToken", "checkTransaction"));
                }},
                new LinkedHashMap<>()));
        methodList.add(new CallRequiredSendMethod(
                "Validator",
                "setAccountOpenState",
                new ArrayList<>() {{
                    add(new CallMethod("FinancialToken", "checkAccountByRegion"));
                }},
                new LinkedHashMap<>()));
        for (CallRequiredSendMethod method : methodList) {
            methodMap.put(String.join(DELIMITER, method.getContractName(), method.getContractMethod()), method);
        }
    }

    /**
     * Send前にCallが必要なメソッドに含まれているか判定する
     *
     * @param contractName   コントラクト名
     * @param contractMethod メソッド名
     * @return 含まれている場合: true
     */
    @NonNull
    public static boolean contains(@NonNull String contractName, @NonNull String contractMethod) {
        return methodMap.containsKey(String.join(DELIMITER, contractName, contractMethod));
    }

    /**
     * 指定したメソッド情報を取得する
     *
     * @param contractName   コントラクト名
     * @param contractMethod メソッド名
     * @return ContractMethod
     */
    @Nullable
    public static CallRequiredSendMethod getContractMethod(@NonNull String contractName, @NonNull String contractMethod) {
        return methodMap.get(String.join(DELIMITER, contractName, contractMethod));
    }
}
