package jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ContractABI {
    private final String name;
    private final String address;
    private final List<ContractFunction> functionList;

    public ContractABI(String name, String address, List<ContractFunction> functionInfoList) {
        this.name = name;
        this.address = address;
        this.functionList = functionInfoList;
    }

    public String getName() {
        return name;
    }

    public String getAddress() {
        return address;
    }

    public ContractFunction[] getFunctionInfos() {
        return functionList.toArray(new ContractFunction[functionList.size()]);
    }

    public ContractFunction getFunctionInfo(String name) throws ContractNotFoundException {
        for (ContractFunction function : getFunctionInfos()) {
            if (function.getName().equals(name)) {
                return function;
            }
        }
        log.info("contractMethod not found");
        throw new ContractNotFoundException("contractMethod not found");
    }
}
