package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.subscribe;

import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.EthDataTypeUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.WebSocketUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.WebSocketConnectionPoolBase;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.exceptions.WebsocketNotConnectedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.web3j.abi.DefaultFunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt;
import org.web3j.protocol.websocket.events.NewHeadsNotification;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigInteger;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class SubscribeService implements ISubscribeService {
    private static final String ERROR_METHOD_ID = "0x08c379a0";
    private static final String NO_REVERT_REASON = "0x";

    private final TransactionDatabase transactionDatabase;

    @Autowired
    public SubscribeService(
            TransactionDatabase transactionDatabase
    ) {
        this.transactionDatabase = transactionDatabase;
    }

    /**
     * SubscribeServiceを実行する。
     * 接続するブロックチェーン領域でトランザクション結果を確認する必要がある場合、isCheckReceiptパラメータをtrueに設定する。
     * @param connectionPool WebSocketコネクションプール
     * @param webSocketHost WebSocketホスト
     * @param webSocketPort WebSocketポート
     * @param isCheckReceipt レシートの確認を実施するか
     * @return NewHeadsのWebSocket購読
     */
    public Disposable execute(
            WebSocketConnectionPoolBase connectionPool,
            String webSocketHost,
            String webSocketPort,
            boolean isCheckReceipt
    ) {
        log.info("Starting subscribe newHeads...");
        // コネクションプール用のWebSocket作成
        connectionPool.createWebSocketConnection(webSocketHost, webSocketPort, false);
        // 購読用のWebSocket作成
        Web3j web3j = Web3j.build(WebSocketUtil.generateWebSocketService(
                webSocketHost,
                webSocketPort,
                false)
        );

        Flowable<NewHeadsNotification> notifications = web3j.newHeadsNotifications();
        Disposable disposable = notifications.subscribe(
                // onNext
                (n) -> {
                    BigInteger blockNumber = Numeric.toBigInt(n.getParams().getResult().getNumber());
                    this.checkReceipt(blockNumber, connectionPool, isCheckReceipt);
                },
                // onError
                (e) -> {
                    log.error("An error occurred in the process of checkReceipt", e);
                    if (e instanceof WebsocketNotConnectedException) {
                        connectionPool.createWebSocketConnection(webSocketHost, webSocketPort, false);
                    }
                });
        log.info("Subscribe newHeads started.");
        return disposable;
    }

    /**
     * Transactionの実行状態を確認する
     *
     * @param blockNumber ブロック番号
     */
    private void checkReceipt(BigInteger blockNumber, WebSocketConnectionPoolBase connectionPool, boolean isCheckReceipt) {
        Web3j web3j = connectionPool.getWebSocketConnection();
        // ブロックデータ取得
        EthBlock ethBlock;
        try {
            ethBlock = web3j.ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), true).send();
        } catch (IOException e) {
            log.error("GetBlockByNumber failed.", e);
            throw new BlockchainIOException("ethGetBlockByNumber.send() failed", e);
        }

        if (!isCheckReceipt) {
            return;
        }

        // トランザクションハッシュ値の確認
        List<EthBlock.TransactionResult> transactionResults = ethBlock.getResult().getTransactions();
        for (EthBlock.TransactionResult transactionResult : transactionResults) {
            EthBlock.TransactionObject transactionObject = (EthBlock.TransactionObject) transactionResult;
            String transactionHash = transactionObject.getHash();
            // broadcastしていないトランザクションは無視する
            if (!transactionDatabase.has(transactionHash)) {
                continue;
            }
            transactionDatabase.toExecuted(transactionHash);

            // レシート取得
            EthGetTransactionReceipt transactionReceipt;
            try {
                transactionReceipt = web3j.ethGetTransactionReceipt(transactionHash).send();
            } catch (IOException e) {
                log.error("GetTransactionReceipt failed.", e);
                throw new BlockchainIOException("ethGetTransactionReceipt.send() failed", e);
            }

            boolean isStatusOK = transactionReceipt.getResult().isStatusOK();
            if (isStatusOK) {
                transactionDatabase.toSuccess(transactionHash);
            } else {
                // decode revert reason
                String revertReason = transactionReceipt.getResult().getRevertReason();
                log.debug("revertReason={}", revertReason);
                Utf8String decodedRevertReason = new Utf8String("");
                if (!NO_REVERT_REASON.equals(revertReason)) {
                    // revert reason: 0x 以外の場合にエラー内容をデコードする
                    String r = revertReason.substring(ERROR_METHOD_ID.length());
                    List<TypeReference<Type>> revertReasonTypes = Collections.singletonList(EthDataTypeUtil.toTypeReference("string"));
                    DefaultFunctionReturnDecoder decoder = new DefaultFunctionReturnDecoder();
                    decodedRevertReason = (Utf8String) decoder.decodeFunctionResult(r, revertReasonTypes).get(0);
                }
                transactionDatabase.toReverted(transactionHash, decodedRevertReason.getValue());
            }
        }
    }
}
