package jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ABIParseException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.AbiFormat;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.ContractABIConvertUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.S3Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ContractInfo {
    private static final int ERROR_EXIT_CODE = 1;

    private ApplicationProperties applicationProperties;
    private S3Util s3Util;
    private Map<String, ContractAddressABIPair> map = new LinkedHashMap<>();

    private ContractInfo() {
    }

    @Autowired
    private ContractInfo(
            ApplicationContext applicationContext,
            ApplicationProperties applicationProperties,
            S3Util s3Util
    ) {
        log.info("Start loading contracts ...");
        this.applicationProperties = applicationProperties;
        this.s3Util = s3Util;
        try {
            String bucketName = applicationProperties.getContractFileBucketName();
            Map<String, byte[]> abiContents = this.getContractABIContents(bucketName);

            AbiFormat abiFormat = applicationProperties.getAbiFormat();
            // .json拡張子以外のファイルを除外し、JSON内容をクラスにマッピングする
            this.filterJsonEntries(abiContents).forEach((String key, byte[] content) -> {
                try {
                    ContractABI abi = ContractABIConvertUtil.convertByteToObject(abiFormat, key, content);
                    map.put(abi.getName(), new ContractAddressABIPair(abi.getAddress(), abi));
                } catch (Exception e) {
                    log.error("Failed to parse ABI file ({}).", key);
                    throw new ABIParseException("ABI file parsing failed", e);
                }
            });
        } catch (Exception e) {
            log.error("ABI file reading error. Stopping application ...", e);
            SpringApplication.exit(applicationContext, () -> ERROR_EXIT_CODE);
        }
        log.info("Loading contracts finished.");
    }

    public ContractABI getContractABI(String contractName) throws ContractNotFoundException {
        if (!map.containsKey(contractName)) {
            throw new ContractNotFoundException("contractName not found");
        }
        return (ContractABI) map.get(contractName).getContractABI();
    }

    /**
     * ファイル名が.jsonで終わるファイル以外を除外する
     *
     * @param abiContents ファイル名とファイル内容のマップ
     * @return .jsonで終わるファイルのマップ
     */
    @NonNull
    Map<String, byte[]> filterJsonEntries(@NonNull Map<String, byte[]> abiContents) {
        return abiContents.entrySet()
                .stream()
                .filter(a -> a.getKey().endsWith(".json"))
                .collect(Collectors.toMap(a -> a.getKey(), a -> a.getValue()));
    }

    private Map<String, byte[]> getContractABIContents(String bucketName) {
        ListObjectsV2Response response = s3Util.listObjects(bucketName);
        List<String> objectKeys = response.contents().stream().map((obj) -> obj.key()).collect(Collectors.toList());
        List<byte[]> bytes = s3Util.getAllObjectsAsByteArrayList(objectKeys, bucketName);
        // ObjectKeyとByteでマッピングする
        Map<String, byte[]> contents = new LinkedHashMap();
        for (int i = 0; i < objectKeys.size(); i++) {
            contents.put(objectKeys.get(i), bytes.get(i));
        }
        return contents;
    }
}
