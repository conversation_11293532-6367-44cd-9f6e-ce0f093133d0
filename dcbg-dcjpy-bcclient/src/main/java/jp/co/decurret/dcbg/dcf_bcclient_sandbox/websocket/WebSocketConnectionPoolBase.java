package jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket;

import io.reactivex.disposables.Disposable;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.WebSocketUtil;
import lombok.Setter;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.web3j.protocol.Web3j;

@Slf4j
public abstract class WebSocketConnectionPoolBase {
    private Web3j web3j;
    @Setter
    private Disposable newHeadsSubscription;

    /**
     * プール用WebSocket接続を作成する
     *
     * @param host                ホスト名
     * @param port                ポート番号
     * @param useSecureConnection 暗号化通信を使用する
     * @return WebSocketService
     */
    @Synchronized
    public Web3j createWebSocketConnection(@NonNull String host, @NonNull String port, @NonNull boolean useSecureConnection) {
        if (this.web3j != null) {
            this.web3j.shutdown();
        }
        this.web3j = Web3j.build(WebSocketUtil.generateWebSocketService(host, port, useSecureConnection));
        return this.web3j;
    }

    /**
     * プールしたWebSocket接続を取得する
     *
     * @return WebSocketService
     */
    public Web3j getWebSocketConnection() {
        if (this.web3j == null) {
            log.error("WebSocket is not initialized.");
            throw new IllegalStateException("WebSocket is not initialized.");
        }
        return this.web3j;
    }

    /**
     * NewHeadsの購読が破棄されているか
     *
     * @return 購読が存在しないか破棄されている場合 true
     */
    public boolean isNewHeadsSubscriptionDisposed() {
        return this.newHeadsSubscription == null || this.newHeadsSubscription.isDisposed();
    }
}
