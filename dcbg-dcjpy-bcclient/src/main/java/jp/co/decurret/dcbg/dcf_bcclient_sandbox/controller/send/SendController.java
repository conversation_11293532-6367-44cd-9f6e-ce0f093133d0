package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.send;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.APIRequestParameter;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.TransferProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.ISendService;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendServiceInput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendServiceOutput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer.ITransferService;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer.TransferServiceInput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer.TransferServiceOutput;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Slf4j
public class SendController {
    private final TransferProperties transferProperties;
    private final ISendService sendService;
    private final ITransferService transferService;

    @Autowired
    public SendController(
            TransferProperties transferProperties,
            ISendService sendService,
            ITransferService transferService
    ) {
        this.transferProperties = transferProperties;
        this.sendService = sendService;
        this.transferService = transferService;
    }

    @RequestMapping(value = "/send", method = RequestMethod.POST, headers = "Accept=application/json")
    @ResponseBody
    public SendResponse send(@RequestBody @Validated APIRequestParameter requestParameter) {
        log.info("Received send request: contractName={}, method={}, args={}.",
                requestParameter.getContractName(),
                requestParameter.getMethod(),
                requestParameter.getArgs()
        );
        // transferのバッチ処理を停止。TODO: https://decurret.atlassian.net/browse/DCFC-6118 にて対応する
//        if (requestParameter.getContractName().equals(this.transferProperties.getTransferContractName())
//                && requestParameter.getMethod().equals(this.transferProperties.getTransferMethodName())) {
//            log.info("Processing the request as transfer.");
//            // transferのコントラクト名とメソッド名が一致した場合
//            TransferServiceOutput transferServiceOutput = this.transfer(requestParameter);
//            log.info("Transfer request response: {}.", transferServiceOutput);
//            return new SendResponse(transferServiceOutput.isResult(), transferServiceOutput.getError(), transferServiceOutput.getTransactionHash());
//        }
        SendServiceOutput serviceOutput = this.sendService.execute(
                new SendServiceInput(
                        requestParameter.getContractName(),
                        requestParameter.getMethod(),
                        requestParameter.getArgs()
                )
        );
        log.info("Send request response: {}.", serviceOutput);
        return new SendResponse(serviceOutput.isResult(), serviceOutput.getError(), serviceOutput.getTransactionHash());
    }

    @NonNull
    private TransferServiceOutput transfer(@NonNull APIRequestParameter requestParameter) {
        TransferServiceOutput serviceOutput = this.transferService.execute(
                new TransferServiceInput(
                        requestParameter.getContractName(),
                        requestParameter.getMethod(),
                        requestParameter.getArgs()
                )
        );
        return serviceOutput;
    }
}