package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.credential.Web3jCredentialPool;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractParameterBase;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.send.SendServiceOutput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.ContractABIConvertUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.EthDataTypeUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.WebSocketConnectionPoolBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.web3j.abi.datatypes.Type;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
@Component
public class EthRequestExecutor {
    private static final int THREAD_SLEEP_TIME = 100; // ms
    private static final BigInteger GAS_PRICE = BigInteger.valueOf(0);

    private final ApplicationProperties applicationProperties;
    private final Web3jCredentialPool web3jCredentialPool;
    private final NonceManager nonceManager;
    private final ContractInfo contractInfo;
    private final TransactionDatabase transactionDatabase;

    @Autowired
    public EthRequestExecutor(
            ApplicationProperties applicationProperties,
            Web3jCredentialPool web3jCredentialPool,
            NonceManager nonceManager,
            ContractInfo contractInfo,
            TransactionDatabase transactionDatabase
    ) {
        this.applicationProperties = applicationProperties;
        this.web3jCredentialPool = web3jCredentialPool;
        this.nonceManager = nonceManager;
        this.contractInfo = contractInfo;
        this.transactionDatabase = transactionDatabase;
    }

    /**
     * Sendを実行する
     *
     * @param contractAddress コントラクトアドレス
     * @param encodedFunction エンコード済みFunction
     * @param connectionPool  コネクションプール
     * @return Sendトランザクション
     */
    @NonNull
    public EthSendTransaction doSend(@NonNull String contractAddress, @NonNull String encodedFunction, @NonNull WebSocketConnectionPoolBase connectionPool) throws IOException {
        Web3j web3j = connectionPool.getWebSocketConnection();

        Credentials credentials = this.web3jCredentialPool.getCredentials();

        BigInteger nonce = nonceManager.getNonce(web3j, credentials.getAddress());
        log.debug("nonce={}", nonce.toString());
        RawTransaction rawTransaction =
                RawTransaction.createTransaction(
                        nonce,
                        GAS_PRICE,
                        BigInteger.valueOf(this.applicationProperties.getGasLimit()),
                        contractAddress,
                        encodedFunction
                );
        // sign transaction
        byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, credentials);
        String hexValue = Numeric.toHexString(signedMessage);
        // broadcast transaction
        return web3j.ethSendRawTransaction(hexValue).send();
    }

    /**
     * Send結果を取得する
     *
     * @param ethSendTransaction Sendトランザクション
     * @return Send結果
     */
    @NonNull
    public SendServiceOutput fetchTransactionResult(@NonNull EthSendTransaction ethSendTransaction) {
        String transactionHash = ethSendTransaction.getTransactionHash();
        if (ethSendTransaction.getError() != null) {
            String errorMessage = ethSendTransaction.getError().getMessage();
            return new SendServiceOutput(false, errorMessage, transactionHash);
        }

        this.transactionDatabase.begin(transactionHash);
        if (ethSendTransaction.getError() != null) {
            transactionDatabase.toRejected(transactionHash);
            String errorMessage = ethSendTransaction.getError().getMessage();
            return new SendServiceOutput(false, errorMessage, transactionHash);
        }

        this.transactionDatabase.toQueued(transactionHash);
        Long startTime = System.currentTimeMillis();
        while (true) {
            try {
                Thread.sleep(THREAD_SLEEP_TIME);
            } catch (InterruptedException e) {
                log.info(e.getMessage());
                break;
            }
            if (System.currentTimeMillis() > startTime + this.applicationProperties.getRequestTimeoutSec()) {
                throw new BlockchainIOException("request timeout");
            }
            if (this.transactionDatabase.isCompleted(transactionHash)) {
                break;
            }
        }

        switch (this.transactionDatabase.getState(transactionHash)) {
            case SUCCESS:
                return new SendServiceOutput(true, "", transactionHash);
            case REVERTED:
                return new SendServiceOutput(false, this.transactionDatabase.getRevertReason(transactionHash), transactionHash);
            default:
                throw new RuntimeException("TransactionState must be SUCCESS or REVERTED.");
        }
    }

    /**
     * Callを実行する
     *
     * @param contractAddress コントラクトアドレス
     * @param encodedFunction エンコード済みFunction
     * @param connectionPool  コネクションプール
     * @return Call結果文字列
     */
    @NonNull
    public EthCall doCall(@NonNull String contractAddress, @NonNull String encodedFunction, @NonNull WebSocketConnectionPoolBase connectionPool) throws IOException {
        Web3j web3j = connectionPool.getWebSocketConnection();
        Credentials credentials = this.web3jCredentialPool.getCredentials();
        // call
        EthCall ethCall = web3j.ethCall(
                Transaction.createEthCallTransaction(credentials.getAddress(), contractAddress, encodedFunction),
                DefaultBlockParameterName.LATEST
        ).send();
        return ethCall;
    }

    /**
     * Call結果のデコードを実施する
     *
     * @param value            エンコードされた結果
     * @return デコード結果
     */
    @NonNull
    public LinkedHashMap decodeCallResult(@NonNull String value, @NonNull ContractFunction contractFunction) {
        LinkedHashMap callResult = new LinkedHashMap();

        List<Type> types = ContractABIConvertUtil.decodeFunction(contractFunction, value);
        for (int i = 0; i < types.size(); i++) {
            ContractParameterBase outputParameter = contractFunction.getOutputParameterList().get(i);
            Type type = types.get(i);
            callResult = this.convertAbiTypeToKeyValue(outputParameter, type, callResult);
        }
        return callResult;
    }

    /**
     * ABI型をKey-Valueに変換する。Array型の場合は再起的に処理し基本型を取り出す。
     *
     * @param outputParameter コントラクトパラメータ
     * @param type            ABI型
     * @param callResult      Call結果
     * @return LinkedHashMap
     */
    private LinkedHashMap convertAbiTypeToKeyValue(ContractParameterBase outputParameter, Type type, LinkedHashMap callResult) {
        callResult.put(outputParameter.getName(), this.getAbiValue(outputParameter.getTypeString(), type));
        return callResult;
    }

    /**
     * ABI型文字列に合わせてABI型の値を取り出す
     *
     * @param typeString ABI型文字列
     * @param type       ABI型
     * @return Object 解析後の値
     */
    @NonNull
    private Object getAbiValue(@NonNull String typeString, @NonNull Type type) {
        if (EthDataTypeUtil.isArray(typeString)) {
            List<Object> values = new ArrayList<>();
            String basicType = EthDataTypeUtil.toBasicTypeString(typeString);
            for (Object o : (List) type.getValue()) {
                // 再起処理
                values.add(this.getAbiValue(basicType, (Type) o));
            }
            return values;
        } else if (EthDataTypeUtil.isBool(typeString)) {
            return type.getValue();
        } else {
            return EthDataTypeUtil.toStringValue(type);
        }
    }
}
