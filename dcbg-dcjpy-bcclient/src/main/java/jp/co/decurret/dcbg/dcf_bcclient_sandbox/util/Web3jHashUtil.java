package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import org.springframework.lang.NonNull;
import org.web3j.abi.DefaultFunctionEncoder;
import org.web3j.abi.datatypes.Type;
import org.web3j.crypto.*;
import org.web3j.utils.Numeric;

import java.util.List;


public class Web3jHashUtil {
    /**
     * 引数から16進数ハッシュ値を生成する
     *
     * @param params 可変長引数
     * @return 16進数ハッシュ値
     */
    @NonNull
    public static String generateHash(@NonNull Type<?>... params) {
        DefaultFunctionEncoder encoder = new DefaultFunctionEncoder();
        String abi = encoder.encodeParameters(List.of(params));
        return Hash.sha3(abi);
    }

    /**
     * クレデンシャルを生成する
     *
     * @return Credentials
     */
    @NonNull
    public static Credentials generateCredentials() {
        Credentials credentials;
        try {
            ECKeyPair ecKeyPair = Keys.createEcKeyPair();
            credentials = Credentials.create(ecKeyPair);
        } catch (Exception e) {
            throw new BlockchainIOException("createECKeyPair failed", e);
        }
        return credentials;
    }

    /**
     * 署名鍵を使用して16進数ハッシュ値を生成する
     *
     * @param hash        ハッシュ値
     * @param credentials 署名鍵
     * @return 署名された16進数ハッシュ値
     */
    @NonNull
    public static String sign(@NonNull String hash, @NonNull Credentials credentials) {
        ECKeyPair pair = credentials.getEcKeyPair();
        Sign.SignatureData sign = Sign.signPrefixedMessage(Numeric.hexStringToByteArray(hash), pair);
        // https://web3js.readthedocs.io/en/v1.3.1/web3-eth-accounts.html#eth-accounts-sign によると、r, s, v の順で繋げたものが signature であるようだ。
        // 専用のメソッドは用意されていないようなので、自前で結合している。
        byte[] r = sign.getR();
        byte[] s = sign.getS();
        byte[] v = sign.getV();
        byte[] signBytes = new byte[r.length + s.length + v.length];
        System.arraycopy(r, 0, signBytes, 0, r.length);
        System.arraycopy(s, 0, signBytes, r.length, s.length);
        System.arraycopy(v, 0, signBytes, r.length + s.length, v.length);
        return Numeric.toHexString(signBytes);
    }
}