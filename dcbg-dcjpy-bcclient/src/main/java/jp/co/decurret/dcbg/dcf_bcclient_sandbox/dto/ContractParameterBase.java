package jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto;

import lombok.Getter;

@Getter
public abstract class ContractParameterBase {
    private final String name;
    private final Class clazz;
    private final String typeString;

    public ContractParameterBase(String name, Class clazz, String typeString) {
        this.name = name;
        this.clazz = clazz;
        this.typeString = typeString;
    }
}