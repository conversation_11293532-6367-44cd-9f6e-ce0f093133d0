package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.AbiFormat;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.ContractABIConvertUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.EthDataTypeUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.S3Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ServiceInputConverter implements IServiceInputConverter {
    private final ApplicationProperties applicationProperties;
    private final S3Util s3Util;

    @Autowired
    public ServiceInputConverter(
            ApplicationProperties applicationProperties,
            S3Util s3Util
    ) {
        this.applicationProperties = applicationProperties;
        this.s3Util = s3Util;
    }

    /**
     * ServiceInputをFunctionエンコードする
     *
     * @param serviceInput     サービスインプット
     * @param contractFunction コントラクトFunction
     * @param contractAddress  コントラクトアドレス
     * @return ConvertedInputData
     */
    @NonNull
    public ConvertedInputData convert(@NonNull ServiceInputBase serviceInput, @NonNull ContractFunction contractFunction, @NonNull String contractAddress) {
        // argsをEthDataに変換しFunctionをエンコードする
        List<Object> inputParams = this.convertInputParamsToEthData(contractFunction, serviceInput.getArgs());
        String encodedFunction = this.encodeFunction(contractFunction, inputParams);
        return new ConvertedInputData(contractAddress, encodedFunction);
    }


    /**
     * コントラクト関数オブジェクトを取得する
     *
     * @param serviceInput サービスインプット
     * @param contractABI  コントラクトABI
     * @return ContractFunction
     * @throws ContractNotFoundException
     */
    public ContractFunction extractContractFunction(@NonNull ServiceInputBase serviceInput, @NonNull ContractABI contractABI) throws ContractNotFoundException {
        return contractABI.getFunctionInfo(serviceInput.getMethod());
    }

    /**
     * 外部S3からコントラクトABIを取得する
     *
     * @param serviceInput
     * @return ContractABI
     */
    public ContractABI extractExternalContractABI(@NonNull ServiceInputBase serviceInput) {
        log.info("Finding an external contract (contractName={}, method={})", serviceInput.getContractName(), serviceInput.getMethod());
        byte[] abiContent;
        String key = String.format("%s.json", serviceInput.getContractName());
        try {
            // 外部ABI用S3からJSONをダウンロードする
            abiContent = this.s3Util.getObjectAsByte(
                    key,
                    applicationProperties.getExternalContractFileBucketName()
            );
        } catch (NoSuchKeyException e) {
            log.warn("No abi file on the external abi S3 (key={})", key);
            throw new BadRequestException("contractName not found");
        }

        AbiFormat abiFormat = applicationProperties.getAbiFormat();
        try {
            return ContractABIConvertUtil.convertByteToObject(abiFormat, key, abiContent);
        } catch (IOException e) {
            log.warn("Failed to convert an external abi (contractName={}, method={})", serviceInput.getContractName(), serviceInput.getMethod());
            throw new RuntimeException(e);
        }
    }

    /**
     * パラメータをEthDataに変換する
     *
     * @param contractFunction コントラクトFunction
     * @param args             パラメータ
     * @return EthData
     */
    @NonNull
    private List<Object> convertInputParamsToEthData(@NonNull ContractFunction contractFunction, @Nullable LinkedHashMap args) {
        if (args == null) {
            return new ArrayList<>();
        }
        return contractFunction.getInputParameterList().stream().map(param -> {
            String key = param.getName();
            if (!args.containsKey(key)) {
                throw new BadRequestException(key + " does not exists");
            }
            try {
                return EthDataTypeUtil.toEthParamObject(param.getTypeString(), args.get(key));
            } catch (UnsupportedEncodingException e) {
                throw new IllegalStateException(e);
            }
        }).collect(Collectors.toList());
    }

    /**
     * コントラクトFunctionをエンコードする
     *
     * @param contractFunction コントラクトFunction
     * @param inputParams      EthData
     * @return エンコード済みFunction
     */
    @NonNull
    private String encodeFunction(@NonNull ContractFunction contractFunction, @NonNull List<Object> inputParams) {
        try {
            return ContractABIConvertUtil.encodeFunction(contractFunction, inputParams);
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage(), e);
        }
    }
}
