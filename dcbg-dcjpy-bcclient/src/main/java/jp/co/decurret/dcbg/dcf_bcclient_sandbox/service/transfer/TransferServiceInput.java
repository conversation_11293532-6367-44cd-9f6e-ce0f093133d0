package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputBase;

import java.util.LinkedHashMap;

public class TransferServiceInput extends ServiceInputBase {
    public TransferServiceInput() {
        super();
    }

    public TransferServiceInput(String contractName, String method, LinkedHashMap args) {
        super(contractName, method, args);
    }
}
