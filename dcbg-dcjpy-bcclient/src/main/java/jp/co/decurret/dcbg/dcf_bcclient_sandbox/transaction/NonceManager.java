package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;

import java.io.IOException;
import java.math.BigInteger;

@Component
public class NonceManager {
    private volatile BigInteger nonce = BigInteger.valueOf(-1);

    /**
     * スレッドセーフでNonceを取得する
     *
     * @return Nonce
     * @throws IOException
     */
    public synchronized BigInteger getNonce(@NonNull Web3j web3j, @NonNull String address) {
        if (nonce.signum() == -1) {
            EthGetTransactionCount ethGetTransactionCount;
            try {
                ethGetTransactionCount = web3j.ethGetTransactionCount(address, DefaultBlockParameterName.PENDING).send();
            } catch (IOException e) {
                throw new BlockchainIOException("ethGetTransactionCount failed", e);
            }
            nonce = ethGetTransactionCount.getTransactionCount();
        } else {
            nonce = nonce.add(BigInteger.ONE);
        }
        return nonce;
    }
}
