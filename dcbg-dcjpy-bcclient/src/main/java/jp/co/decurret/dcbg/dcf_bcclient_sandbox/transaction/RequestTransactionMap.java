package jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class RequestTransactionMap {
    /**
     * ReqeustIdとTransactionHashの紐付け管理 (key: RequestId)
     */
    private final Map<String, String> requestTransaction = new ConcurrentHashMap<>();

    /**
     * リクエストIDをキーにしてトランザクションハッシュとの紐付けを登録する
     *
     * @param requestId       リクエストID
     * @param transactionHash トランザクションハッシュ
     */
    public void putValue(String requestId, String transactionHash) {
        if (requestId == null || requestId.isEmpty()) {
            return;
        }
        this.requestTransaction.put(requestId, transactionHash);
    }

    /**
     * 指定したリクエストIDを削除する
     *
     * @param requestId リクエストID
     */
    public void removeKey(@NonNull String requestId) {
        if (requestId == null || requestId.isEmpty()) {
            return;
        }
        log.info("Removed requestId: {}", requestId);
        this.requestTransaction.remove(requestId);
    }

    /**
     * 指定したリクエストIDと紐付くトランザクションハッシュを取得する
     *
     * @param requestId リクエストID
     * @return トランザクションハッシュ
     */
    @Nullable
    public String getTransactionHash(@NonNull String requestId) {
        return this.requestTransaction.get(requestId);
    }

    /**
     * トランザクションハッシュを保持状態を確認する
     *
     * @param transactionHash トランザクションハッシュ
     * @return トランザクションハッシュを保持している場合: true
     */
    @NonNull
    public boolean hasTransactionHash(@NonNull String transactionHash) {
        return this.requestTransaction.containsValue(transactionHash);
    }
}
