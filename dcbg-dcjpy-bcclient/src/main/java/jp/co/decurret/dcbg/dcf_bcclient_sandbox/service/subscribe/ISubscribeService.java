package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.subscribe;

import io.reactivex.disposables.Disposable;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.WebSocketConnectionPoolBase;

public interface ISubscribeService {
    Disposable execute(
            WebSocketConnectionPoolBase connectionPool,
            String webSocketHost,
            String webSocketPort,
            boolean isCheckReceipt
    );
}
