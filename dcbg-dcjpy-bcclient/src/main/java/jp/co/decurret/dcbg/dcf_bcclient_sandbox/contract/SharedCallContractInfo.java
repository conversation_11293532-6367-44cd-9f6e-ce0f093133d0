package jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract;

import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;

/**
 * 残高管理DLTにCallリクエストを送信する必要があるメソッド情報
 */
public class SharedCallContractInfo {
    private static final List<String> contractNameList = new ArrayList<>();

    static {
        contractNameList.add("FinancialAccount");
        contractNameList.add("FinancialToken");
        contractNameList.add("FinancialValidator");
    }

    /**
     * 残高管理DLTにCallが必要なコントラクト名に含まれているか判定する
     *
     * @param contractName コントラクト名
     * @return 含まれている場合: true
     */
    @NonNull
    public static boolean contains(@NonNull String contractName) {
        return contractNameList.contains(contractName);
    }
}
