package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.SQSProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.TransferProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ConvertedInputData;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.RequestTransactionMap;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.SQSUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import software.amazon.awssdk.services.sqs.model.Message;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
@Service
public class TransferPeriodicExecutionService implements ITransferPeriodicExecutionService {
    private static final String ERROR_HASH = "0x00";

    private final SQSProperties sqsProperties;
    private final TransferProperties transferProperties;
    private final ContractInfo contractInfo;
    private final ServiceInputConverter serviceInputConverter;
    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;
    private final TransactionDatabase transactionDatabase;
    private final RequestTransactionMap requestTransactionMap;
    private final EthRequestExecutor ethRequestExecutor;
    private final SQSUtil sqsUtil;

    @Autowired
    public TransferPeriodicExecutionService(
            SQSProperties sqsProperties,
            TransferProperties transferProperties,
            ContractInfo contractInfo,
            ServiceInputConverter serviceInputConverter,
            MainWebSocketConnectionPool mainWebSocketConnectionPool,
            TransactionDatabase transactionDatabase,
            RequestTransactionMap requestTransactionMap,
            EthRequestExecutor ethRequestExecutor,
            SQSUtil sqsUtil
    ) {
        this.sqsProperties = sqsProperties;
        this.transferProperties = transferProperties;
        this.contractInfo = contractInfo;
        this.serviceInputConverter = serviceInputConverter;
        this.mainWebSocketConnectionPool = mainWebSocketConnectionPool;
        this.transactionDatabase = transactionDatabase;
        this.requestTransactionMap = requestTransactionMap;
        this.ethRequestExecutor = ethRequestExecutor;
        this.sqsUtil = sqsUtil;
    }

    /**
     * キューからtransferリクエストを取得しBatch実行する
     */
    public void execute() {
        // キューからの取り出し
        List<Message> messages = this.sqsUtil.receiveMessage(
                this.sqsProperties.getMaxFetchSize(),
                this.sqsProperties.getSqsQueueUri()
        );
        if (messages.size() == 0) {
            // 1件もメッセージがない場合、これ以降の処理はしない
            return;
        }
        log.info("Processing {} queue message[s] in transferBatch.", messages.size());
        // TransferBatch
        EthSendTransaction ethSendTransaction = null;
        try {
            ethSendTransaction = this.transferSend(messages);
            // 結果取得。状態管理で共有しているのでリクエストプロセスに結果が通知される
            this.ethRequestExecutor.fetchTransactionResult(ethSendTransaction);
        } catch(Exception e) {
            log.error("error in transferBatch.", e);
            // トランザクションが投げられていない場合、一時ハッシュで紐付ける
            String transactionHash = ethSendTransaction != null ? ethSendTransaction.getTransactionHash() : ERROR_HASH;
            this.transactionDatabase.toReverted(transactionHash, "error in send transfer");
        } finally {
            // トランザクションが投げられていない場合、一時ハッシュで紐付ける
            String transactionHash = ethSendTransaction != null ? ethSendTransaction.getTransactionHash() : ERROR_HASH;
            // メッセージIDとハッシュとの紐付け
            messages.forEach(m -> {
                this.requestTransactionMap.putValue(m.messageId(), transactionHash);
            });
            // キューからの削除
            this.sqsUtil.deleteMessageBatch(messages, this.sqsProperties.getSqsQueueUri());
        }
    }

    /**
     * transferリクエストを実行する
     *
     * @param messages 複数件のtransferリクエスト
     * @return EthSendTransaction
     */
    EthSendTransaction transferSend(List<Message> messages) throws IOException {
        // 取り出しがある場合に文字列からオブジェクトに戻す
        ObjectMapper mapper = new ObjectMapper();
        List<TransferServiceInput> transferServiceInputList = new ArrayList<>();
        for (Message message : messages) {
            try {
                transferServiceInputList.add(mapper.readValue(message.body(), TransferServiceInput.class));
            } catch (JsonProcessingException e) {
                // 変換できないリクエストは無視する
                log.error("failed to convert json into object", e);
            }
        }
        // batchリクエストパラメータの作成
        ConvertedInputData convertedInputData = null;
        try {
            TransferServiceInput transferServiceInput = this.convertInputToBatchParam(transferServiceInputList);
            ContractABI contractABI = contractInfo.getContractABI(transferServiceInput.getContractName());
            ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(transferServiceInput, contractABI);
            convertedInputData = this.serviceInputConverter.convert(transferServiceInput, contractFunction, contractABI.getAddress());

        } catch (ContractNotFoundException e) {
            throw new BadRequestException(e);
        }
        // Sendリクエストの実行
        try {
            log.info("doTransferSend: contractName={}, method={}.", this.transferProperties.getTransferContractName(), this.transferProperties.getTransferBatchMethodName());
            return this.ethRequestExecutor.doSend(
                    convertedInputData.getContractAddress(),
                    convertedInputData.getEncodedFunction(),
                    this.mainWebSocketConnectionPool // メイン領域側で実行
            );
        } catch (IOException e) {
            log.error("Send request in transferBatch failed.", e);
            throw e;
        }
    }

    /**
     * BatchTransferリクエストパラメータに変換する
     *
     * @param transferServiceInputList Transferサービスインプット
     * @return BatchTransferリクエストパラメータ
     */
    TransferServiceInput convertInputToBatchParam(List<TransferServiceInput> transferServiceInputList) {
        TransferServiceInput batchInput = new TransferServiceInput();
        batchInput.setContractName(this.transferProperties.getTransferContractName());
        batchInput.setMethod(this.transferProperties.getTransferBatchMethodName());
        // 配列に入れ替える
        LinkedHashMap<String, Object> batchArgs = new LinkedHashMap();
        for (TransferServiceInput serviceInput : transferServiceInputList) {
            LinkedHashMap<String, Object> args = serviceInput.getArgs();
            // transferリクエスト受付時に必要なパラメータを確認しているのですべて入れ替えてしまってよい
            // また、パラメータが余剰にある場合でもencodeFunction時に必要なパラメータのみ取得されるので問題なし
            for (String property : args.keySet()) {
                if (!batchArgs.containsKey(property)) {
                    // リストが存在しない場合初期化
                    batchArgs.put(property, new ArrayList<>());
                }
                ((List)batchArgs.get(property)).add(args.get(property));
            }
        }
        batchInput.setArgs(batchArgs);
        return batchInput;
    }
}
