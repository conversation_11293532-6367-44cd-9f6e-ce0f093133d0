package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.transfer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.SQSProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.TransferProperties;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ConvertedInputData;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.RequestTransactionMap;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.TransactionDatabase;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.SQSUtil;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.web3j.protocol.core.methods.response.EthCall;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.UUID;

@Slf4j
@Service
public class TransferService implements ITransferService {
    private static final int THREAD_SLEEP_TIME = 100; // ms
    private static final String MESSAGE_GROUP_ID = "transfer-group";

    private final TransferProperties transferProperties;
    private final SQSProperties sqsProperties;
    private final ContractInfo contractInfo;
    private final ServiceInputConverter serviceInputConverter;
    private final TransactionDatabase transactionDatabase;
    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;
    private final SubWebSocketConnectionPool subWebSocketConnectionPool;
    private final EthRequestExecutor ethRequestExecutor;
    private final SQSUtil sqsUtil;
    private final RequestTransactionMap requestTransactionMap;

    @Autowired
    public TransferService(
            TransferProperties transferProperties,
            SQSProperties sqsProperties,
            ContractInfo contractInfo,
            ServiceInputConverter serviceInputConverter,
            TransactionDatabase transactionDatabase,
            MainWebSocketConnectionPool mainWebSocketConnectionPool,
            SubWebSocketConnectionPool subWebSocketConnectionPool,
            EthRequestExecutor ethRequestExecutor,
            SQSUtil sqsUtil,
            RequestTransactionMap requestTransactionMap
    ) {
        this.transferProperties = transferProperties;
        this.sqsProperties = sqsProperties;
        this.contractInfo = contractInfo;
        this.serviceInputConverter = serviceInputConverter;
        this.transactionDatabase = transactionDatabase;
        this.mainWebSocketConnectionPool = mainWebSocketConnectionPool;
        this.subWebSocketConnectionPool = subWebSocketConnectionPool;
        this.ethRequestExecutor = ethRequestExecutor;
        this.sqsUtil = sqsUtil;
        this.requestTransactionMap = requestTransactionMap;
    }

    @Override
    @NonNull
    public TransferServiceOutput execute(@NonNull TransferServiceInput transferServiceInput) {
        // TransferSingleにマッピングすることでパラメータ過不足を検証する
        this.checkTransferParams(transferServiceInput);
        // callによる検証
        LinkedHashMap callResult = this.transferCall(transferServiceInput);
        Object success =  callResult.get("success");
        if (success == null ) {
            throw new BlockchainIOException("callCheck success not found");
        }
        if (Boolean.FALSE.equals(success)) {
            throw new BadRequestException((String) callResult.get("err"));
        }
        // リクエストをキューイングする
        SendMessageResponse sendMessageResponse = this.enqueueRequest(transferServiceInput);
        String messageId = sendMessageResponse.messageId();
        log.info("Transfer request enqueued: messageId={} .", messageId);
        // リクエストIDに紐付くトランザクションハッシュを取得
        String transactionHash;
        while (true) {
            try {
                Thread.sleep(THREAD_SLEEP_TIME);
            } catch (InterruptedException e) {
                log.warn(e.getMessage());
                throw new IllegalStateException(e);
            }
            transactionHash = this.requestTransactionMap.getTransactionHash(messageId);
            if (transactionHash != null && !transactionHash.isEmpty()) {
                break;
            }
        }
        // トランザクション結果の取得
        try {
            return this.fetchTransferResult(transactionHash);
        } finally {
            this.requestTransactionMap.removeKey(messageId);
            if (!this.requestTransactionMap.hasTransactionHash(transactionHash)) {
                // リクエストをすべて返した後に削除する
                this.transactionDatabase.removeKey(transactionHash);
            }
        }
    }

    /**
     * TransferSingleにマッピングすることでパラメータの過不足をチェックする
     *
     * @param transferServiceInput Transferサービスインプット
     * @throws BadRequestException パラメータが不足している場合
     */
    void checkTransferParams(@NonNull TransferServiceInput transferServiceInput) {
        TransferServiceInput transferSingleInput = new TransferServiceInput(
                this.transferProperties.getTransferContractName(),
                this.transferProperties.getTransferSingleMethodName(),
                transferServiceInput.getArgs()
        );
        try {
            ContractABI contractABI = contractInfo.getContractABI(transferSingleInput.getContractName());
            ContractFunction contractFunction = this.serviceInputConverter.extractContractFunction(transferSingleInput, contractABI);
            this.serviceInputConverter.convert(transferSingleInput, contractFunction, contractABI.getAddress());
        } catch (ContractNotFoundException e) {
            throw new BadRequestException("contractMethod not found");
        }
    }

    /**
     * Transferリクエストのトランザクション検証を実施するためにCallリクエストを実行する
     *
     * @param transferServiceInput Transferサービスインプット
     * @return Call結果内容
     */
    @NonNull
    LinkedHashMap transferCall(@NonNull TransferServiceInput transferServiceInput) {
        // コントラクト名とメソッド名を書き換えて共有DLTで検証する
        CallServiceInput callServiceInput = new CallServiceInput(
                this.transferProperties.getTransferCallContractName(),
                this.transferProperties.getTransferCallMethodName(),
                transferServiceInput.getArgs()
        );
        ContractABI contractABI;
        ContractFunction contractFunction;
        try {
            contractABI = contractInfo.getContractABI(callServiceInput.getContractName());
            contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI);
        } catch (ContractNotFoundException e) {
            throw new BadRequestException("contractMethod not found");
        }
        ConvertedInputData convertedInputData = this.serviceInputConverter.convert(callServiceInput, contractFunction, contractABI.getAddress());

        log.info("Call request in transfer: contractName={}, method={}, args={}.", callServiceInput.getContractName(), callServiceInput.getMethod(), callServiceInput.getArgs());

        EthCall ethCall;
        try {
            ethCall = this.ethRequestExecutor.doCall(
                    convertedInputData.getContractAddress(),
                    convertedInputData.getEncodedFunction(),
                    this.subWebSocketConnectionPool // 共有DLT側のコネクションを使用する
            );
        } catch (IOException e) {
            log.error("Call request in transfer failed.", e);
            throw new BlockchainIOException("failed to ethCall", e);
        }
        return this.ethRequestExecutor.decodeCallResult(ethCall.getValue(), contractFunction);
    }

    /**
     * transferリクエストをキューイングする
     *
     * @param transferServiceInput サービスインプット
     * @return SendMessageResponse キューイング結果
     */
    @NonNull
    SendMessageResponse enqueueRequest(@NonNull TransferServiceInput transferServiceInput) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            String json = mapper.writeValueAsString(transferServiceInput);
            log.debug("converted transfer json: {}.", json);
            return this.sqsUtil.sendMessage(
                    json,
                    this.sqsProperties.getSqsQueueUri(),
                    MESSAGE_GROUP_ID,
                    UUID.randomUUID().toString()
            );
        } catch (JsonProcessingException e) {
            log.error("failed to convert transfer input into json.", e);
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * transfer結果を取得する
     *
     * @param transactionHash トランザクションハッシュ
     * @return TransferServiceOutput
     */
    @NonNull
    TransferServiceOutput fetchTransferResult(@NonNull String transactionHash) {
        while (true) {
            try {
                Thread.sleep(THREAD_SLEEP_TIME);
            } catch (InterruptedException e) {
                log.info(e.getMessage());
                throw new IllegalStateException(e);
            }
            if (this.transactionDatabase.isCompleted(transactionHash)) {
                break;
            }
        }
        switch (this.transactionDatabase.getState(transactionHash)) {
            case SUCCESS:
                return new TransferServiceOutput(true, "", transactionHash);
            case REVERTED:
                return new TransferServiceOutput(false, this.transactionDatabase.getRevertReason(transactionHash), transactionHash);
            default:
                throw new RuntimeException("TransactionState must be SUCCESS or REVERTED.");
        }
    }
}
