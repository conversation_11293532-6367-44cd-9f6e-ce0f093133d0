package jp.co.decurret.dcbg.dcf_bcclient_sandbox.credential;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.util.Web3jHashUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.web3j.crypto.Credentials;

@Slf4j
@Component
public class Web3jCredentialPool {
    private final Credentials credentials;

    public Web3jCredentialPool() {
        this.credentials = Web3jHashUtil.generateCredentials();
        log.info("Credentials created.");
    }

    /**
     * Credentialsを取得する
     *
     * @return Credentials
     */
    public Credentials getCredentials() {
        return this.credentials;
    }
}
