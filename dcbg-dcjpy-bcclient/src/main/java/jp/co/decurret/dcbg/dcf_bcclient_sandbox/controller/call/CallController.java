package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.call;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller.APIRequestParameter;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceInput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.CallServiceOutput;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call.ICallService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import lombok.extern.slf4j.Slf4j;

@Controller
@Slf4j
public class CallController {
    private final ICallService callService;

    @Autowired
    public CallController(ICallService callService) {
        this.callService = callService;
    }

    @RequestMapping(value = "/call", method = RequestMethod.POST, headers = "Accept=application/json")
    @ResponseBody
    public CallResponse call(@RequestBody @Validated APIRequestParameter requestParameter) {
        log.info("Received call request: contractName={}, method={}, args={}.",
                requestParameter.getContractName(),
                requestParameter.getMethod(),
                requestParameter.getArgs()
        );
        CallServiceOutput serviceOutput = this.callService.execute(
                new CallServiceInput(
                        requestParameter.getContractName(),
                        requestParameter.getMethod(),
                        requestParameter.getArgs()
                )
        );
        log.info("Call request response: {}.", serviceOutput.getHashMapData());
        return new CallResponse(serviceOutput.getHashMapData());
    }
}