package jp.co.decurret.dcbg.dcf_bcclient_sandbox.controller;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.NoRevertReasonException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@ControllerAdvice
public class ExceptionHandlerControllerAdvice extends ResponseEntityExceptionHandler {

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<GenericErrorResponse> handleBadRequest(BadRequestException ex) {
        logger.warn("Handles bad request", ex);
        HttpStatus status = HttpStatus.BAD_REQUEST;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @ExceptionHandler(BlockchainIOException.class)
    public ResponseEntity<GenericErrorResponse> handleBadRequest(BlockchainIOException ex) {
        logger.warn("Handles bad request", ex);
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @ExceptionHandler(NoRevertReasonException.class)
    public  ResponseEntity<GenericErrorResponse> handleNoRevertReasonTransaction(NoRevertReasonException ex) {
        logger.warn("Handles no revert reason transaction result", ex);
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers, HttpStatus status, WebRequest request) {
        logger.warn("Handles method argument not valid", ex);
        String message = "";
        FieldError fieldError = ex.getBindingResult().getFieldError();
        if (fieldError != null) {
            message = fieldError.getField() + ":" + fieldError.getDefaultMessage();
        }
        return new ResponseEntity<>(new GenericErrorResponse(
                status.value(),
                status.getReasonPhrase(),
                message
        ), status);
    }

    private GenericErrorResponse createResponse(HttpStatus status, Exception ex) {
        return new GenericErrorResponse(status.value(), status.getReasonPhrase(), ex.getMessage());
    }
}
