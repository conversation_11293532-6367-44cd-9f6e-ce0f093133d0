package jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix="transfer")
public class TransferProperties {
    private String transferContractName;
    private String transferMethodName;
    private String transferSingleMethodName;
    private String transferBatchMethodName;
    private String transferCallContractName;
    private String transferCallMethodName;
}
