package jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.call;

import jp.co.decurret.dcbg.dcf_bcclient_sandbox.contract.SharedCallContractInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractABI;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractFunction;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.dto.ContractInfo;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BadRequestException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.BlockchainIOException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.exception.ContractNotFoundException;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ConvertedInputData;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.service.ServiceInputConverter;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.transaction.EthRequestExecutor;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.MainWebSocketConnectionPool;
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.websocket.SubWebSocketConnectionPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.web3j.protocol.core.methods.response.EthCall;

import java.io.IOException;
import java.util.LinkedHashMap;

@Service
@Slf4j
public class CallService implements ICallService {
    private final ContractInfo contractInfo;
    private final ServiceInputConverter serviceInputConverter;
    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;
    private final SubWebSocketConnectionPool subWebSocketConnectionPool;
    private final EthRequestExecutor ethRequestExecutor;

    @Autowired
    public CallService(
            ContractInfo contractInfo,
            ServiceInputConverter serviceInputConverter,
            MainWebSocketConnectionPool mainWebSocketConnectionPool,
            SubWebSocketConnectionPool subWebSocketConnectionPool,
            EthRequestExecutor ethRequestExecutor
    ) {
        this.contractInfo = contractInfo;
        this.serviceInputConverter = serviceInputConverter;
        this.mainWebSocketConnectionPool = mainWebSocketConnectionPool;
        this.subWebSocketConnectionPool = subWebSocketConnectionPool;
        this.ethRequestExecutor = ethRequestExecutor;
    }

    /**
     * CallServiceを実行する
     *
     * @param callServiceInput サービスインプット
     * @return CallServiceOutput
     */
    @NonNull
    @Override
    public CallServiceOutput execute(@NonNull CallServiceInput callServiceInput) {
        // input変換
        ContractABI contractABI;
        ContractFunction contractFunction;
        try {
            contractABI = this.contractInfo.getContractABI(callServiceInput.getContractName());
            contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI);
        } catch (ContractNotFoundException e) {
            // 外部ABIから変換する
            log.info("No matching internal contracts.");
            try {
                contractABI = this.serviceInputConverter.extractExternalContractABI(callServiceInput);
                contractFunction = this.serviceInputConverter.extractContractFunction(callServiceInput, contractABI);
            } catch (ContractNotFoundException contractNotFoundException) {
                throw new BadRequestException("contractMethod not found");
            }
        }
        ConvertedInputData convertedInputData = this.serviceInputConverter.convert(callServiceInput, contractFunction, contractABI.getAddress());

        // call
        EthCall ethCall;
        try {
            if (SharedCallContractInfo.contains(callServiceInput.getContractName())) {
                // 残高管理DLT側
                log.info("Sub WebSocket doCall.");
                ethCall = this.ethRequestExecutor.doCall(
                        convertedInputData.getContractAddress(),
                        convertedInputData.getEncodedFunction(),
                        this.subWebSocketConnectionPool
                );
            } else {
                // 共通・付加DLT側
                log.info("Main WebSocket doCall.");
                ethCall = this.ethRequestExecutor.doCall(
                        convertedInputData.getContractAddress(),
                        convertedInputData.getEncodedFunction(),
                        this.mainWebSocketConnectionPool
                );
            }
        } catch (IOException e) {
            log.error("Call request failed.", e);
            throw new BlockchainIOException("failed to ethCall", e);
        }
        // call結果のデコード
        LinkedHashMap data;
        try {
            data = this.ethRequestExecutor.decodeCallResult(ethCall.getValue(), contractFunction);
        } catch (Exception e) {
            log.error("Decoding call result failed.", e);
            throw new RuntimeException("failed to decode ethCall return object", e);
        }
        return new CallServiceOutput(data);
    }
}