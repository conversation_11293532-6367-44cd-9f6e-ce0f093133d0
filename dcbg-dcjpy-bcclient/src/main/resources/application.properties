# サーバー設定
server.port=8081
# アプリケーション設定
## スケジュールに使用するスレッド数
spring.task.scheduling.pool.size=5

# ABIファイルのフォーマット指定
app.abiFormat=${ABI_FORMAT:hardhat}

## メインWebSocket
app.webSocketUriHost=${WEBSOCKET_URI_HOST}
app.webSocketUriPort=${WEBSOCKET_URI_PORT}
## サブWebSocket
app.useSubWebSocket=${USE_SUB_WEBSOCKET}
app.subWebSocketUriHost=${SUB_WEBSOCKET_URI_HOST}
app.subWebSocketUriPort=${SUB_WEBSOCKET_URI_PORT}
app.requestTimeoutSec=${REQUEST_TIMEOUT_SEC}
app.region=${REGION}
app.contractFileBucketName=${CONTRACT_BUCKET_NAME}
app.externalContractFileBucketName=${EXTERNAL_CONTRACT_BUCKET_NAME}
app.subscriptionCheckInterval=${SUBSCRIPTION_CHECK_INTERVAL:3000}
app.gasLimit=${GAS_LIMIT}
# SQS
sqs.sqsQueueUri=${SQS_QUEUE_URI}
sqs.maxFetchSize=${SQS_MAX_FETCH_SIZE:5}
# Transfer
transfer.sendTransferInterval=${SEND_TRANSFER_INTERVAL:2000}
transfer.transferContractName=${TRANSFER_CONTRACT_NAME}
transfer.transferMethodName=${TRANSFER_METHOD_NAME}
transfer.transferSingleMethodName=${TRANSFER_SINGLE_METHOD_NAME}
transfer.transferBatchMethodName=${TRANSFER_BATCH_METHOD_NAME}
transfer.transferCallContractName=${TRANSFER_CALL_CONTRACT_NAME}
transfer.transferCallMethodName=${TRANSFER_CALL_METHOD_NAME}
# ログ設定
# Ignoring profile 'xxx' on line 5 because it did not start with 'profile ' and it was not 'default'. というログを表示させないようにする
logging.level.software.amazon.awssdk.profiles.internal.ProfileFileReader=ERROR
