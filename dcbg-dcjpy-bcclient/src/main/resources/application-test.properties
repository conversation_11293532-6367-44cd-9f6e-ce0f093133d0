# アプリケーション設定
## スケジュールに使用するスレッド数
spring.task.scheduling.pool.size=5
## メインWebSocket
app.webSocketUriHost=${WEBSOCKET_URI_HOST:localhost}
app.webSocketUriPort=${WEBSOCKET_URI_PORT:8541}
## サブWebSocket
app.useSubWebSocket=${USE_SUB_WEBSOCKET:true}
app.subWebSocketUriHost=${SUB_WEBSOCKET_URI_HOST:localhost}
app.subWebSocketUriPort=${SUB_WEBSOCKET_URI_PORT:8541}
app.requestTimeoutSec=${REQUEST_TIMEOUT_SEC:10000}
app.region=${REGION:ap-northeast-1}
app.contractFileBucketName=${CONTRACT_BUCKET_NAME:abijson-local-bucket}
app.externalContractFileBucketName=${EXTERNAL_CONTRACT_BUCKET_NAME:external-abijson-local-bucket}
app.subscriptionCheckInterval=${SUBSCRIPTION_CHECK_INTERVAL:3000}
app.gasLimit=${GAS_LIMIT:6721975}
# SQS
sqs.sqsQueueUri=${SQS_QUEUE_URI:http://localhost:9326/queue/test-queue}
sqs.maxFetchSize=${SQS_MAX_FETCH_SIZE:5}
# Transfer
transfer.sendTransferInterval=${SEND_TRANSFER_INTERVAL:2000}
transfer.transferContractName=${TRANSFER_CONTRACT_NAME:Token}
transfer.transferMethodName=${TRANSFER_METHOD_NAME:transferSingle}
transfer.transferSingleMethodName=${TRANSFER_SINGLE_METHOD_NAME:transferSingle}
transfer.transferBatchMethodName=${TRANSFER_BATCH_METHOD_NAME:transferBatch}
transfer.transferCallContractName=${TRANSFER_CALL_CONTRACT_NAME:FinancialToken}
transfer.transferCallMethodName=${TRANSFER_CALL_METHOD_NAME:checkTransaction}
# ローカル設定
## S3
local.useLocalS3Bucket=${USE_LOCAL_S3:true}
local.localS3Uri=http://localhost:9001
local.s3AccessKey=access123
local.s3SecretKey=secret123
## SQS
local.useLocalSQS=${USE_LOCAL_SQS:true}
local.localSQSUri=http://localhost:9326
local.sqsAccessKey=access123
local.sqsSecretKey=secret123
# テスト設定
spring.main.allow-bean-definition-overriding=true
